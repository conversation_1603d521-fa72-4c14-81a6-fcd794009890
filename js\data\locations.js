/**
 * Location Data - All locations in the corruption simulator world
 */

export const locationData = {
    'Home': {
        name: 'Home',
        description: 'Your 2BHK flat in Malviya Nagar. The center of your corruption empire.',
        type: 'residential',
        unlocked: true,
        
        areas: {
            'Living Room': {
                description: 'The main family area where everyone gathers',
                privacy: 'low',
                activities: ['watch_tv', 'family_time', 'conversations'],
                characters_present: ['sonia', 'natasha', 'tanya']
            },
            'Kitchen': {
                description: 'Where <PERSON> prepares meals for the family',
                privacy: 'medium',
                activities: ['cooking', 'private_talk', 'help_cooking'],
                characters_present: ['sonia']
            },
            'Your Room': {
                description: 'Your private sanctuary and planning headquarters',
                privacy: 'high',
                activities: ['system_access', 'planning', 'rest', 'private_activities'],
                characters_present: []
            },
            'Sonia\'s Room': {
                description: 'Your mother\'s bedroom - off limits normally',
                privacy: 'high',
                activities: ['search_secrets', 'plant_items', 'night_visits'],
                characters_present: ['sonia'],
                access_requirements: ['stealth', 'permission', 'night_time']
            },
            'Sisters\' Room': {
                description: 'Shared room of <PERSON> and <PERSON>',
                privacy: 'medium',
                activities: ['search_secrets', 'plant_items', 'conversations'],
                characters_present: ['natasha', 'tanya'],
                access_requirements: ['permission', 'stealth']
            },
            'Bathroom': {
                description: 'Shared family bathroom - opportunities for "accidents"',
                privacy: 'high',
                activities: ['spy', 'plant_cameras', 'accidental_encounters'],
                characters_present: [],
                special_events: ['shower_encounters', 'hidden_cameras']
            }
        },
        
        travel_connections: {
            'School': { time: 30, cost: 0, method: 'walk' },
            'Mall': { time: 45, cost: 50, method: 'auto' },
            'Park': { time: 20, cost: 0, method: 'walk' },
            'Temple': { time: 25, cost: 0, method: 'walk' },
            'Market': { time: 15, cost: 0, method: 'walk' }
        },
        
        secrets: [],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    },
    
    'School': {
        name: 'Delhi Public School',
        description: 'Your school - a hunting ground full of potential targets and opportunities.',
        type: 'educational',
        unlocked: true,
        
        areas: {
            'Classroom': {
                description: 'Where you attend classes and observe targets',
                privacy: 'low',
                activities: ['attend_class', 'observe_targets', 'pass_notes', 'group_work'],
                characters_present: ['kavya', 'aishwarya', 'mehak']
            },
            'Library': {
                description: 'Quiet study area perfect for private conversations',
                privacy: 'medium',
                activities: ['study', 'private_tutoring', 'secret_meetings'],
                characters_present: ['kavya', 'aishwarya']
            },
            'Canteen': {
                description: 'Social hub where gossip spreads and alliances form',
                privacy: 'low',
                activities: ['eat', 'socialize', 'spread_rumors', 'gather_intel'],
                characters_present: ['kavya', 'aishwarya', 'mehak']
            },
            'Empty Classroom': {
                description: 'Unused classrooms perfect for private activities',
                privacy: 'high',
                activities: ['private_tutoring', 'intimate_encounters', 'blackmail_meetings'],
                characters_present: [],
                access_requirements: ['stealth', 'key_access']
            },
            'Principal\'s Office': {
                description: 'The seat of school authority - potential for corruption',
                privacy: 'high',
                activities: ['meetings', 'bribery', 'blackmail', 'record_access'],
                characters_present: ['principal'],
                access_requirements: ['appointment', 'stealth']
            },
            'Sports Ground': {
                description: 'Outdoor area for sports and physical activities',
                privacy: 'medium',
                activities: ['sports', 'physical_training', 'outdoor_meetings'],
                characters_present: ['kavya', 'sports_teacher']
            }
        },
        
        travel_connections: {
            'Home': { time: 30, cost: 0, method: 'walk' },
            'Mall': { time: 20, cost: 30, method: 'auto' },
            'Park': { time: 15, cost: 0, method: 'walk' },
            'Tuition Center': { time: 10, cost: 0, method: 'walk' }
        },
        
        secrets: [
            'Teacher affair scandal',
            'Student grade manipulation',
            'Illegal activities in empty classrooms'
        ],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    },
    
    'Mall': {
        name: 'Select City Walk',
        description: 'Upscale shopping mall - perfect for displays of wealth and corruption.',
        type: 'commercial',
        unlocked: false,
        unlock_requirements: ['wealth_display', 'social_status'],
        
        areas: {
            'Food Court': {
                description: 'Popular dining area where people gather',
                privacy: 'low',
                activities: ['dining', 'people_watching', 'social_meetings'],
                characters_present: ['natasha', 'kavya']
            },
            'Clothing Stores': {
                description: 'High-end fashion stores for the wealthy',
                privacy: 'medium',
                activities: ['shopping', 'gift_giving', 'status_display'],
                characters_present: ['natasha', 'sales_girls']
            },
            'Cinema': {
                description: 'Movie theater with dark, private spaces',
                privacy: 'high',
                activities: ['movie_dates', 'intimate_moments', 'public_corruption'],
                characters_present: []
            },
            'Parking Garage': {
                description: 'Secluded area perfect for private activities',
                privacy: 'high',
                activities: ['private_meetings', 'car_encounters', 'blackmail_exchanges'],
                characters_present: [],
                special_events: ['car_corruption', 'stalking_opportunities']
            },
            'Luxury Stores': {
                description: 'Expensive boutiques for the ultra-wealthy',
                privacy: 'medium',
                activities: ['luxury_shopping', 'sugar_daddy_activities', 'status_corruption'],
                characters_present: ['natasha'],
                access_requirements: ['high_wealth']
            }
        },
        
        travel_connections: {
            'Home': { time: 45, cost: 50, method: 'auto' },
            'School': { time: 20, cost: 30, method: 'auto' },
            'Hotel': { time: 10, cost: 20, method: 'auto' }
        },
        
        secrets: [],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    },
    
    'Park': {
        name: 'Lodhi Gardens',
        description: 'Public park with secluded areas perfect for private meetings.',
        type: 'recreational',
        unlocked: true,
        
        areas: {
            'Main Path': {
                description: 'Well-lit main walking path with regular foot traffic',
                privacy: 'low',
                activities: ['walking', 'jogging', 'casual_meetings'],
                characters_present: ['natasha', 'random_people']
            },
            'Secluded Grove': {
                description: 'Hidden area among trees, perfect for privacy',
                privacy: 'high',
                activities: ['private_meetings', 'intimate_encounters', 'secret_exchanges'],
                characters_present: []
            },
            'Bench Area': {
                description: 'Quiet seating area for conversations',
                privacy: 'medium',
                activities: ['conversations', 'people_watching', 'planning'],
                characters_present: []
            }
        },
        
        travel_connections: {
            'Home': { time: 20, cost: 0, method: 'walk' },
            'School': { time: 15, cost: 0, method: 'walk' },
            'Mall': { time: 30, cost: 40, method: 'auto' }
        },
        
        secrets: [],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    },
    
    'Temple': {
        name: 'Hanuman Temple',
        description: 'Local temple where Sonia goes for prayers - opportunities for religious corruption.',
        type: 'religious',
        unlocked: true,
        
        areas: {
            'Main Hall': {
                description: 'Main prayer area with many devotees',
                privacy: 'low',
                activities: ['prayers', 'religious_activities', 'social_meetings'],
                characters_present: ['sonia', 'devotees']
            },
            'Side Chambers': {
                description: 'Smaller prayer rooms with more privacy',
                privacy: 'medium',
                activities: ['private_prayers', 'counseling', 'intimate_conversations'],
                characters_present: ['sonia']
            },
            'Temple Grounds': {
                description: 'Outdoor area around the temple',
                privacy: 'medium',
                activities: ['walking', 'meditation', 'private_talks'],
                characters_present: []
            }
        },
        
        travel_connections: {
            'Home': { time: 25, cost: 0, method: 'walk' },
            'Market': { time: 10, cost: 0, method: 'walk' }
        },
        
        secrets: [],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    },
    
    'Market': {
        name: 'Local Market',
        description: 'Busy local market where Sonia shops for groceries.',
        type: 'commercial',
        unlocked: true,
        
        areas: {
            'Vegetable Stalls': {
                description: 'Fresh produce vendors',
                privacy: 'low',
                activities: ['shopping', 'bargaining', 'social_interaction'],
                characters_present: ['sonia', 'vendors']
            },
            'Clothing Shops': {
                description: 'Local clothing stores',
                privacy: 'medium',
                activities: ['shopping', 'trying_clothes', 'gift_buying'],
                characters_present: ['sonia', 'natasha']
            }
        },
        
        travel_connections: {
            'Home': { time: 15, cost: 0, method: 'walk' },
            'Temple': { time: 10, cost: 0, method: 'walk' }
        },
        
        secrets: [],
        discovered_secrets: [],
        installed_equipment: [],
        corruption_level: 0
    }
};

/**
 * Location Manager - Handles location-based mechanics
 */
export class LocationManager {
    constructor(stateManager, timeManager) {
        this.stateManager = stateManager;
        this.timeManager = timeManager;
    }
    
    /**
     * Initialize locations in world state
     */
    initializeLocations() {
        const worldState = this.stateManager.state.world;
        
        Object.keys(locationData).forEach(locationId => {
            const locData = locationData[locationId];
            
            if (!worldState.locations[locationId]) {
                worldState.locations[locationId] = {
                    unlocked: locData.unlocked,
                    discovered_secrets: [],
                    installed_equipment: [],
                    corruption_level: 0,
                    visit_count: 0,
                    last_visit: null
                };
            }
        });
    }
    
    /**
     * Check if location is unlocked
     */
    isLocationUnlocked(locationId) {
        const worldLoc = this.stateManager.state.world.locations[locationId];
        return worldLoc && worldLoc.unlocked;
    }
    
    /**
     * Unlock location
     */
    unlockLocation(locationId) {
        const worldLoc = this.stateManager.state.world.locations[locationId];
        if (worldLoc) {
            worldLoc.unlocked = true;
            this.stateManager.triggerAutoSave();
            
            // Emit unlock event
            if (window.gameEngine) {
                window.gameEngine.emit('world:location_unlock', { locationId });
            }
        }
    }
    
    /**
     * Get travel time between locations
     */
    getTravelTime(fromLocation, toLocation) {
        const locData = locationData[fromLocation];
        if (!locData || !locData.travel_connections[toLocation]) {
            return { time: 60, cost: 100, method: 'auto' }; // Default
        }
        
        return locData.travel_connections[toLocation];
    }
    
    /**
     * Travel to location
     */
    travelToLocation(locationId) {
        const player = this.stateManager.getPlayer();
        const currentLocation = player.location;
        
        if (currentLocation === locationId) {
            return { success: false, message: 'Already at this location' };
        }
        
        if (!this.isLocationUnlocked(locationId)) {
            return { success: false, message: 'Location is locked' };
        }
        
        const travelInfo = this.getTravelTime(currentLocation, locationId);
        
        // Check if player has enough money for travel cost
        if (travelInfo.cost > 0 && player.stats.wealth < travelInfo.cost) {
            return { success: false, message: 'Not enough money for travel' };
        }
        
        // Deduct travel cost
        if (travelInfo.cost > 0) {
            player.stats.wealth -= travelInfo.cost;
        }
        
        // Advance time
        this.timeManager.advanceTime('minutes', travelInfo.time);
        
        // Update player location
        player.location = locationId;
        
        // Update location visit stats
        const worldLoc = this.stateManager.state.world.locations[locationId];
        worldLoc.visit_count++;
        worldLoc.last_visit = Date.now();
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Traveled to ${locationData[locationId].name}`,
            time_taken: travelInfo.time,
            cost: travelInfo.cost,
            method: travelInfo.method
        };
    }
    
    /**
     * Get available areas in current location
     */
    getAvailableAreas(locationId) {
        const locData = locationData[locationId];
        if (!locData) return [];
        
        return Object.entries(locData.areas).map(([areaId, areaData]) => ({
            id: areaId,
            ...areaData
        }));
    }
    
    /**
     * Get characters present at location
     */
    getCharactersAtLocation(locationId, timeSlot, dayOfWeek) {
        const locData = locationData[locationId];
        if (!locData) return [];
        
        const charactersPresent = [];
        
        // Check each character's schedule
        Object.keys(this.stateManager.state.characters).forEach(charId => {
            const char = this.stateManager.getCharacter(charId);
            if (char && char.schedule_base && char.schedule_base[dayOfWeek]) {
                const charLocation = char.schedule_base[dayOfWeek][timeSlot];
                if (charLocation === locationId) {
                    charactersPresent.push(charId);
                }
            }
        });
        
        return charactersPresent;
    }
    
    /**
     * Get location events based on current state
     */
    getLocationEvents(locationId) {
        const locData = locationData[locationId];
        const worldLoc = this.stateManager.state.world.locations[locationId];
        const currentTime = this.timeManager.getCurrentDayInfo();
        
        const events = [];
        
        // Check for special events based on time, corruption level, etc.
        if (locData.type === 'residential' && currentTime.timeSlot === 'Night') {
            events.push({
                id: 'night_activities',
                name: 'Night Opportunities',
                description: 'The house is quiet... perfect for corruption activities',
                type: 'opportunity'
            });
        }
        
        if (locData.type === 'educational' && currentTime.isSchoolHours) {
            events.push({
                id: 'school_activities',
                name: 'School Activities',
                description: 'Classes are in session, targets are available',
                type: 'social'
            });
        }
        
        return events;
    }
}
