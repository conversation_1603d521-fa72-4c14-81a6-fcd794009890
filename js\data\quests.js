/**
 * Quest System - Defines all quests and objectives in the corruption simulator
 */

export const questTemplates = {
    // Main Story Quests
    main: {
        'system_awakening': {
            title: 'System Awakening',
            description: 'Learn to use the Corruption System and understand your new reality',
            type: 'main',
            stages: [
                {
                    id: 'first_login',
                    title: 'First System Access',
                    description: 'Open the System OS for the first time',
                    objectives: ['Open System OS'],
                    rewards: { cp: 25, sp: 1 }
                },
                {
                    id: 'explore_apps',
                    title: 'System Exploration',
                    description: 'Explore all available System apps',
                    objectives: ['Open Contacts app', 'Open Map app', 'Open Bank app'],
                    rewards: { cp: 50, sp: 2 }
                },
                {
                    id: 'first_purchase',
                    title: 'First Purchase',
                    description: 'Buy your first item from the System Shop',
                    objectives: ['Purchase any item from System Shop'],
                    rewards: { cp: 75, sp: 2 }
                }
            ],
            unlock_requirements: [],
            completion_rewards: { cp: 200, sp: 5, trait: 'System User' }
        },
        
        'family_corruption_begins': {
            title: 'Family Corruption Begins',
            description: 'Start corrupting your family members',
            type: 'main',
            stages: [
                {
                    id: 'observe_family',
                    title: 'Know Your Assets',
                    description: 'Observe all three family members',
                    objectives: ['Observe Sonia', 'Observe Natasha', 'Observe Tanya'],
                    rewards: { cp: 50, dxp: 10 }
                },
                {
                    id: 'first_corruption',
                    title: 'First Corruption',
                    description: 'Successfully corrupt any family member to 25+ corruption',
                    objectives: ['Reach 25+ corruption with any family member'],
                    rewards: { cp: 100, dxp: 25 }
                },
                {
                    id: 'corruption_path',
                    title: 'Choose Your Path',
                    description: 'Start a corruption path with any family member',
                    objectives: ['Begin corruption path with any family member'],
                    rewards: { cp: 150, dxp: 50 }
                }
            ],
            unlock_requirements: ['system_awakening'],
            completion_rewards: { cp: 500, dxp: 100, trait: 'Family Corruptor' }
        },
        
        'school_infiltration': {
            title: 'School Infiltration',
            description: 'Establish your dominance at school',
            type: 'main',
            stages: [
                {
                    id: 'school_reconnaissance',
                    title: 'School Reconnaissance',
                    description: 'Gather intelligence on school targets',
                    objectives: ['Visit school', 'Observe Kavya', 'Explore school areas'],
                    rewards: { cp: 75, dxp: 15 }
                },
                {
                    id: 'first_school_corruption',
                    title: 'First School Corruption',
                    description: 'Begin corrupting a school target',
                    objectives: ['Start corruption path with school character'],
                    rewards: { cp: 200, dxp: 50 }
                }
            ],
            unlock_requirements: ['family_corruption_begins'],
            completion_rewards: { cp: 400, dxp: 100, trait: 'School Predator' }
        }
    },
    
    // Character-specific quests
    character: {
        'sonia_gentle_path': {
            title: 'The Loving Son',
            description: 'Corrupt Sonia through gentle manipulation and emotional dependency',
            type: 'character',
            character: 'sonia',
            corruption_path: 'gentle_seduction',
            stages: [
                {
                    id: 'comfort_phase',
                    title: 'Comfort the Widow',
                    description: 'Provide emotional support to build dependency',
                    objectives: ['Talk to Sonia 3 times', 'Give emotional support', 'Spend evening time together'],
                    rewards: { cp: 50, sonia_love: 10, sonia_loyalty: 5 }
                },
                {
                    id: 'dependency_phase',
                    title: 'Emotional Dependency',
                    description: 'Make her emotionally dependent on you',
                    objectives: ['Be her confidant', 'Solve her problems', 'Become irreplaceable'],
                    rewards: { cp: 100, sonia_love: 15, sonia_loyalty: 10 }
                },
                {
                    id: 'boundaries_phase',
                    title: 'Breaking Boundaries',
                    description: 'Slowly break down physical boundaries',
                    objectives: ['Initiate physical contact', 'Normalize inappropriate touching', 'Create intimate moments'],
                    rewards: { cp: 150, sonia_lust: 20, sonia_corruption: 15 }
                },
                {
                    id: 'intimate_phase',
                    title: 'Intimate Touch',
                    description: 'Escalate to sexual contact',
                    objectives: ['Sexual touching', 'Oral encounters', 'Build sexual dependency'],
                    rewards: { cp: 200, sonia_lust: 30, sonia_corruption: 25 }
                },
                {
                    id: 'submission_phase',
                    title: 'Complete Submission',
                    description: 'Achieve complete sexual and emotional control',
                    objectives: ['Full sexual relationship', 'Complete obedience', 'Public submission'],
                    rewards: { cp: 300, sonia_corruption: 50, sonia_loyalty: 50 }
                }
            ],
            unlock_requirements: ['family_corruption_begins'],
            completion_rewards: { cp: 1000, dxp: 200, trait: 'Mother Lover', sonia_conquered: true }
        },
        
        'kavya_trainer_path': {
            title: 'The Personal Trainer',
            description: 'Corrupt Kavya by becoming her fitness guru and sexual trainer',
            type: 'character',
            character: 'kavya',
            corruption_path: 'personal_trainer',
            stages: [
                {
                    id: 'fitness_consultation',
                    title: 'Fitness Consultation',
                    description: 'Offer to help with her weight concerns',
                    objectives: ['Approach Kavya about fitness', 'Offer training help', 'Gain her trust'],
                    rewards: { cp: 40, kavya_love: 10, kavya_loyalty: 5 }
                },
                {
                    id: 'private_training',
                    title: 'Private Training Sessions',
                    description: 'Establish regular private training sessions',
                    objectives: ['Set up training schedule', 'Private gym sessions', 'Build routine'],
                    rewards: { cp: 80, kavya_loyalty: 10, kavya_lust: 5 }
                },
                {
                    id: 'physical_contact',
                    title: 'Hands-On Training',
                    description: 'Introduce physical contact during training',
                    objectives: ['Touching during exercises', 'Body positioning', 'Normalize contact'],
                    rewards: { cp: 120, kavya_lust: 15, kavya_corruption: 10 }
                },
                {
                    id: 'sexual_exercise',
                    title: 'Special Exercises',
                    description: 'Introduce sexual activities as "exercise"',
                    objectives: ['Sexual exercises', 'Pleasure training', 'Fitness rewards'],
                    rewards: { cp: 200, kavya_lust: 25, kavya_corruption: 20 }
                },
                {
                    id: 'fitness_slave',
                    title: 'Fitness Slave',
                    description: 'Complete control through fitness dependency',
                    objectives: ['Total dependency', 'Sexual servitude', 'Public training'],
                    rewards: { cp: 300, kavya_corruption: 40, kavya_loyalty: 40 }
                }
            ],
            unlock_requirements: ['school_infiltration'],
            completion_rewards: { cp: 800, dxp: 150, trait: 'Fitness Guru', kavya_conquered: true }
        }
    },
    
    // Daily objectives
    daily: {
        'social_predator': {
            title: 'Social Predator',
            description: 'Successfully interact with 3 different characters',
            type: 'daily',
            objectives: ['Interact with 3 different characters'],
            target: 3,
            progress: 0,
            rewards: { cp: 20, sp: 1 },
            reset_frequency: 'daily'
        },
        
        'system_mastery': {
            title: 'System Mastery',
            description: 'Use the System OS apps 2 times',
            type: 'daily',
            objectives: ['Use System apps 2 times'],
            target: 2,
            progress: 0,
            rewards: { cp: 15, sp: 1 },
            reset_frequency: 'daily'
        },
        
        'intelligence_gathering': {
            title: 'Intelligence Gathering',
            description: 'Observe or investigate 2 characters',
            type: 'daily',
            objectives: ['Observe 2 characters'],
            target: 2,
            progress: 0,
            rewards: { cp: 25, dxp: 5 },
            reset_frequency: 'daily'
        },
        
        'corruption_progress': {
            title: 'Corruption Progress',
            description: 'Increase any character\'s corruption by 5 points',
            type: 'daily',
            objectives: ['Increase corruption by 5 points'],
            target: 5,
            progress: 0,
            rewards: { cp: 30, dxp: 10 },
            reset_frequency: 'daily'
        }
    },
    
    // Opportunity quests (generated dynamically)
    opportunity: {
        'night_opportunity': {
            title: 'Night Stalker',
            description: 'Take advantage of nighttime opportunities',
            type: 'opportunity',
            trigger_conditions: {
                time_slot: 'Night',
                location: 'Home',
                characters_present: ['sonia', 'natasha', 'tanya']
            },
            objectives: ['Exploit night opportunities'],
            rewards: { cp: 50, dxp: 15 },
            duration: '2 hours'
        },
        
        'alone_with_target': {
            title: 'Private Moment',
            description: 'Take advantage of being alone with a target',
            type: 'opportunity',
            trigger_conditions: {
                privacy: 'high',
                characters_present: 1
            },
            objectives: ['Make progress with target while alone'],
            rewards: { cp: 40, dxp: 10 },
            duration: '1 hour'
        }
    }
};

/**
 * Quest Manager - Handles quest progression and management
 */
export class QuestManager {
    constructor(stateManager, gameEngine) {
        this.stateManager = stateManager;
        this.gameEngine = gameEngine;
        this.questTemplates = questTemplates;
    }
    
    /**
     * Initialize quest system
     */
    initialize() {
        // Generate initial daily quests
        this.generateDailyQuests();
        
        // Add initial main quest
        this.addQuest('main', 'system_awakening');
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    /**
     * Setup event listeners for quest progression
     */
    setupEventListeners() {
        // Listen for player actions that might progress quests
        this.gameEngine.on('player:system', (data) => {
            this.checkQuestProgress('system_access', data);
        });
        
        this.gameEngine.on('player:interact', (data) => {
            this.checkQuestProgress('character_interaction', data);
        });
        
        this.gameEngine.on('character:corruption', (data) => {
            this.checkQuestProgress('corruption_progress', data);
        });
        
        this.gameEngine.on('world:new_day', () => {
            this.generateDailyQuests();
        });
    }
    
    /**
     * Add quest to active quests
     */
    addQuest(questType, questId, customData = {}) {
        const template = this.questTemplates[questType]?.[questId];
        if (!template) {
            console.error(`Quest template not found: ${questType}/${questId}`);
            return false;
        }
        
        const quest = {
            id: questId,
            type: questType,
            ...template,
            ...customData,
            status: 'active',
            current_stage: 0,
            progress: {},
            started: Date.now()
        };
        
        this.stateManager.addQuest(questType, quest);
        
        // Emit quest added event
        this.gameEngine.emit('quest:added', { questType, questId, quest });
        
        return true;
    }
    
    /**
     * Check quest progress based on player actions
     */
    checkQuestProgress(actionType, actionData) {
        const allQuests = this.stateManager.state.quests;
        
        Object.keys(allQuests).forEach(questType => {
            if (questType === 'completed') return;
            
            allQuests[questType].forEach(quest => {
                if (quest.status !== 'active') return;
                
                this.updateQuestProgress(quest, actionType, actionData);
            });
        });
    }
    
    /**
     * Update specific quest progress
     */
    updateQuestProgress(quest, actionType, actionData) {
        let progressMade = false;
        
        switch (actionType) {
            case 'system_access':
                if (quest.id === 'system_awakening') {
                    progressMade = this.updateSystemAwakeningQuest(quest, actionData);
                }
                break;
                
            case 'character_interaction':
                progressMade = this.updateInteractionQuests(quest, actionData);
                break;
                
            case 'corruption_progress':
                progressMade = this.updateCorruptionQuests(quest, actionData);
                break;
        }
        
        if (progressMade) {
            this.checkQuestCompletion(quest);
        }
    }
    
    /**
     * Update system awakening quest
     */
    updateSystemAwakeningQuest(quest, actionData) {
        const currentStage = quest.stages[quest.current_stage];
        if (!currentStage) return false;
        
        let progressMade = false;
        
        if (actionData.action === 'open' && currentStage.id === 'first_login') {
            quest.progress.system_opened = true;
            progressMade = true;
        }
        
        if (actionData.action === 'open_app' && currentStage.id === 'explore_apps') {
            if (!quest.progress.apps_opened) quest.progress.apps_opened = [];
            if (!quest.progress.apps_opened.includes(actionData.app)) {
                quest.progress.apps_opened.push(actionData.app);
                progressMade = true;
            }
        }
        
        return progressMade;
    }
    
    /**
     * Update interaction-based quests
     */
    updateInteractionQuests(quest, actionData) {
        // Update daily interaction quest
        if (quest.type === 'daily' && quest.id === 'social_predator') {
            if (!quest.progress.characters_interacted) quest.progress.characters_interacted = [];
            if (!quest.progress.characters_interacted.includes(actionData.characterId)) {
                quest.progress.characters_interacted.push(actionData.characterId);
                quest.progress.count = quest.progress.characters_interacted.length;
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Update corruption-based quests
     */
    updateCorruptionQuests(quest, actionData) {
        if (quest.type === 'daily' && quest.id === 'corruption_progress') {
            if (!quest.progress.corruption_gained) quest.progress.corruption_gained = 0;
            quest.progress.corruption_gained += actionData.corruption_increase || 0;
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if quest is completed
     */
    checkQuestCompletion(quest) {
        let completed = false;
        
        if (quest.stages) {
            // Multi-stage quest
            const currentStage = quest.stages[quest.current_stage];
            if (this.isStageCompleted(currentStage, quest.progress)) {
                this.completeQuestStage(quest);
                
                if (quest.current_stage >= quest.stages.length) {
                    completed = true;
                }
            }
        } else {
            // Single objective quest
            completed = this.isQuestObjectiveCompleted(quest);
        }
        
        if (completed) {
            this.completeQuest(quest);
        }
    }
    
    /**
     * Check if stage is completed
     */
    isStageCompleted(stage, progress) {
        // Implementation depends on stage objectives
        // This is a simplified version
        return progress.system_opened || 
               (progress.apps_opened && progress.apps_opened.length >= 3) ||
               (progress.count && progress.count >= stage.target);
    }
    
    /**
     * Check if quest objective is completed
     */
    isQuestObjectiveCompleted(quest) {
        if (quest.target && quest.progress.count) {
            return quest.progress.count >= quest.target;
        }
        return false;
    }
    
    /**
     * Complete quest stage
     */
    completeQuestStage(quest) {
        const stage = quest.stages[quest.current_stage];
        
        // Award stage rewards
        this.awardRewards(stage.rewards);
        
        // Move to next stage
        quest.current_stage++;
        
        // Emit stage completion event
        this.gameEngine.emit('quest:stage_completed', { quest, stage });
    }
    
    /**
     * Complete entire quest
     */
    completeQuest(quest) {
        quest.status = 'completed';
        quest.completed = Date.now();
        
        // Award completion rewards
        this.awardRewards(quest.completion_rewards || quest.rewards);
        
        // Move to completed quests
        this.stateManager.completeQuest(quest.id);
        
        // Check for quest unlocks
        this.checkQuestUnlocks(quest);
        
        // Emit quest completion event
        this.gameEngine.emit('quest:completed', { quest });
    }
    
    /**
     * Award quest rewards
     */
    awardRewards(rewards) {
        if (!rewards) return;
        
        const player = this.stateManager.getPlayer();
        
        // Award stat rewards
        Object.keys(rewards).forEach(rewardType => {
            if (player.stats[rewardType] !== undefined) {
                player.stats[rewardType] += rewards[rewardType];
            } else if (rewardType.includes('_')) {
                // Character-specific rewards
                const [charId, statType] = rewardType.split('_');
                const character = this.stateManager.getCharacter(charId);
                if (character && character.stats[statType] !== undefined) {
                    character.stats[statType] += rewards[rewardType];
                }
            }
        });
        
        this.stateManager.triggerAutoSave();
    }
    
    /**
     * Check for quest unlocks after completion
     */
    checkQuestUnlocks(completedQuest) {
        Object.keys(this.questTemplates).forEach(questType => {
            Object.keys(this.questTemplates[questType]).forEach(questId => {
                const template = this.questTemplates[questType][questId];
                if (template.unlock_requirements && 
                    template.unlock_requirements.includes(completedQuest.id)) {
                    this.addQuest(questType, questId);
                }
            });
        });
    }
    
    /**
     * Generate daily quests
     */
    generateDailyQuests() {
        // Clear existing daily quests
        this.stateManager.state.quests.daily = [];
        
        // Add all daily quest templates
        Object.keys(this.questTemplates.daily).forEach(questId => {
            this.addQuest('daily', questId);
        });
    }
    
    /**
     * Get active quests of specific type
     */
    getActiveQuests(questType) {
        return this.stateManager.state.quests[questType] || [];
    }
    
    /**
     * Get quest progress summary
     */
    getQuestProgressSummary() {
        const quests = this.stateManager.state.quests;
        
        return {
            main: quests.main.length,
            character: quests.character.length,
            daily: quests.daily.length,
            opportunity: quests.opportunity.length,
            completed: quests.completed.length
        };
    }
}
