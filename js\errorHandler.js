// User-Friendly Error Handler for The Predator's Ascent
// Makes errors feel like part of the game experience

export class GameErrorHandler {
    constructor() {
        this.errorContainer = null;
        this.setupErrorContainer();
        this.setupGlobalErrorHandling();
    }

    setupErrorContainer() {
        // Create a beautiful error display container
        this.errorContainer = document.createElement('div');
        this.errorContainer.id = 'game-error-container';
        this.errorContainer.className = 'game-error-container hidden';
        this.errorContainer.innerHTML = `
            <div class="error-modal">
                <div class="error-header">
                    <div class="error-title">[SYSTEM ALERT]</div>
                    <button class="error-close" onclick="gameErrorHandler.hideError()">×</button>
                </div>
                <div class="error-content">
                    <div class="error-message"></div>
                    <div class="error-actions"></div>
                </div>
            </div>
        `;
        document.body.appendChild(this.errorContainer);
    }

    setupGlobalErrorHandling() {
        // Catch all JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError('unexpected', event.error?.message || 'Something unexpected happened');
        });

        // Catch unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError('loading', 'Failed to load game content');
            event.preventDefault();
        });
    }

    handleError(type, details = '') {
        const errorInfo = this.getErrorInfo(type, details);
        this.showError(errorInfo);
    }

    getErrorInfo(type, details) {
        const errorTypes = {
            'chapter-missing': {
                title: '[CHAPTER ACCESS DENIED]',
                message: `
                    <p>🔒 <strong>The System has detected an issue accessing the next chapter.</strong></p>
                    <p>This might happen if:</p>
                    <ul>
                        <li>The chapter file is missing or corrupted</li>
                        <li>Gemini is still building this chapter</li>
                        <li>There's a temporary loading issue</li>
                    </ul>
                `,
                actions: [
                    { label: 'Try Again', action: 'retry-chapter' },
                    { label: 'Go Back to Previous Chapter', action: 'previous-chapter' },
                    { label: 'Restart Game', action: 'restart-game' }
                ]
            },
            'loading': {
                title: '[LOADING INTERRUPTED]',
                message: `
                    <p>⚡ <strong>The System encountered a loading issue.</strong></p>
                    <p>Don't worry - your progress is safe! This usually fixes itself.</p>
                    <p><em>Details: ${details}</em></p>
                `,
                actions: [
                    { label: 'Try Again', action: 'retry-loading' },
                    { label: 'Refresh Page', action: 'refresh-page' }
                ]
            },
            'save-error': {
                title: '[SAVE SYSTEM ERROR]',
                message: `
                    <p>💾 <strong>Unable to save your progress right now.</strong></p>
                    <p>Your current session is still safe, but we recommend:</p>
                    <ul>
                        <li>Don't close the browser tab</li>
                        <li>Try the action again in a moment</li>
                        <li>Check your browser's storage settings</li>
                    </ul>
                `,
                actions: [
                    { label: 'Continue Playing', action: 'continue' },
                    { label: 'Try Saving Again', action: 'retry-save' }
                ]
            },
            'unexpected': {
                title: '[SYSTEM GLITCH DETECTED]',
                message: `
                    <p>🔧 <strong>The System experienced a minor glitch.</strong></p>
                    <p>This is usually temporary and doesn't affect your progress.</p>
                    <p><em>Technical details: ${details}</em></p>
                `,
                actions: [
                    { label: 'Continue Playing', action: 'continue' },
                    { label: 'Refresh Page', action: 'refresh-page' },
                    { label: 'Report Issue', action: 'report-issue' }
                ]
            },
            'network': {
                title: '[CONNECTION ISSUE]',
                message: `
                    <p>🌐 <strong>Network connection seems unstable.</strong></p>
                    <p>The game works offline once loaded, but some features might be limited.</p>
                `,
                actions: [
                    { label: 'Continue Offline', action: 'continue' },
                    { label: 'Check Connection', action: 'retry-connection' }
                ]
            }
        };

        return errorTypes[type] || errorTypes['unexpected'];
    }

    showError(errorInfo) {
        const messageDiv = this.errorContainer.querySelector('.error-message');
        const actionsDiv = this.errorContainer.querySelector('.error-actions');
        const titleDiv = this.errorContainer.querySelector('.error-title');

        titleDiv.textContent = errorInfo.title;
        messageDiv.innerHTML = errorInfo.message;

        // Create action buttons
        actionsDiv.innerHTML = '';
        errorInfo.actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'error-action-button';
            button.textContent = action.label;
            button.onclick = () => this.handleAction(action.action);
            actionsDiv.appendChild(button);
        });

        // Show the error with animation
        this.errorContainer.classList.remove('hidden');
        setTimeout(() => {
            this.errorContainer.classList.add('visible');
        }, 10);
    }

    hideError() {
        this.errorContainer.classList.remove('visible');
        setTimeout(() => {
            this.errorContainer.classList.add('hidden');
        }, 300);
    }

    handleAction(action) {
        switch (action) {
            case 'retry-chapter':
                this.hideError();
                // Try to reload the current chapter
                if (window.main && window.main.retryCurrentChapter) {
                    window.main.retryCurrentChapter();
                }
                break;

            case 'previous-chapter':
                this.hideError();
                // Go back to previous chapter
                if (window.main && window.main.goToPreviousChapter) {
                    window.main.goToPreviousChapter();
                }
                break;

            case 'restart-game':
                this.hideError();
                if (confirm('Are you sure you want to restart? Your current progress will be lost.')) {
                    window.location.reload();
                }
                break;

            case 'retry-loading':
                this.hideError();
                // Attempt to retry the last action
                window.location.reload();
                break;

            case 'refresh-page':
                window.location.reload();
                break;

            case 'continue':
                this.hideError();
                break;

            case 'retry-save':
                this.hideError();
                // Attempt to save again
                if (window.gameState && window.gameState.save) {
                    window.gameState.save();
                }
                break;

            case 'retry-connection':
                this.hideError();
                // Test connection and retry
                this.testConnection();
                break;

            case 'report-issue':
                this.hideError();
                this.showReportDialog();
                break;
        }
    }

    testConnection() {
        fetch('.')
            .then(() => {
                this.showSuccess('Connection restored! 🎉');
            })
            .catch(() => {
                this.handleError('network');
            });
    }

    showSuccess(message) {
        // Show a brief success message
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);

        setTimeout(() => {
            successDiv.classList.add('visible');
        }, 10);

        setTimeout(() => {
            successDiv.classList.remove('visible');
            setTimeout(() => {
                document.body.removeChild(successDiv);
            }, 300);
        }, 3000);
    }

    showReportDialog() {
        const reportInfo = `
            <div class="report-dialog">
                <h3>Report an Issue</h3>
                <p>To help improve the game, you can:</p>
                <ul>
                    <li>Take a screenshot of the error</li>
                    <li>Note what you were doing when it happened</li>
                    <li>Check the browser console (F12) for technical details</li>
                </ul>
                <p>The game is designed to be self-healing, so most issues resolve automatically!</p>
                <button onclick="gameErrorHandler.hideError()">Got it!</button>
            </div>
        `;
        
        const messageDiv = this.errorContainer.querySelector('.error-message');
        messageDiv.innerHTML = reportInfo;
        
        const actionsDiv = this.errorContainer.querySelector('.error-actions');
        actionsDiv.innerHTML = '';
    }
}

// Create global instance
export const gameErrorHandler = new GameErrorHandler();

// Make it globally accessible
window.gameErrorHandler = gameErrorHandler;
