/**
 * Consequence System - Handles action consequences, reputation, and dynamic world responses
 */

export const consequenceData = {
    // Reputation categories and their effects
    reputation_categories: {
        'family': {
            name: 'Family Reputation',
            description: 'How your family views you',
            effects: {
                'very_negative': { threshold: -80, effects: ['family_hostility', 'kicked_out_risk'] },
                'negative': { threshold: -40, effects: ['family_suspicion', 'restricted_access'] },
                'neutral': { threshold: 0, effects: [] },
                'positive': { threshold: 40, effects: ['family_trust', 'increased_access'] },
                'very_positive': { threshold: 80, effects: ['family_devotion', 'complete_access'] }
            }
        },
        
        'school': {
            name: 'School Reputation',
            description: 'How school community views you',
            effects: {
                'very_negative': { threshold: -80, effects: ['expelled_risk', 'teacher_hostility'] },
                'negative': { threshold: -40, effects: ['teacher_suspicion', 'peer_avoidance'] },
                'neutral': { threshold: 0, effects: [] },
                'positive': { threshold: 40, effects: ['teacher_favor', 'peer_respect'] },
                'very_positive': { threshold: 80, effects: ['school_privileges', 'leadership_roles'] }
            }
        },
        
        'social': {
            name: 'Social Reputation',
            description: 'General social standing in community',
            effects: {
                'very_negative': { threshold: -80, effects: ['social_isolation', 'police_attention'] },
                'negative': { threshold: -40, effects: ['gossip_spread', 'opportunity_loss'] },
                'neutral': { threshold: 0, effects: [] },
                'positive': { threshold: 40, effects: ['social_opportunities', 'network_expansion'] },
                'very_positive': { threshold: 80, effects: ['community_leader', 'influence_bonus'] }
            }
        }
    },
    
    // Action consequences mapping
    action_consequences: {
        // Character interaction consequences
        'character_interaction': {
            'observe': {
                immediate: [],
                delayed: [
                    { condition: 'repeated_observation', effect: 'suspicion_increase', target: 'observed_character' }
                ]
            },
            
            'seduce': {
                immediate: [
                    { condition: 'success', effect: 'lust_increase', target: 'target_character' },
                    { condition: 'failure', effect: 'suspicion_increase', target: 'target_character' }
                ],
                delayed: [
                    { condition: 'public_location', effect: 'reputation_decrease', target: 'social', delay: '1 day' },
                    { condition: 'family_member', effect: 'family_tension', target: 'family', delay: '2 hours' }
                ]
            },
            
            'intimidate': {
                immediate: [
                    { condition: 'success', effect: 'fear_increase', target: 'target_character' },
                    { condition: 'failure', effect: 'reputation_decrease', target: 'social' }
                ],
                delayed: [
                    { condition: 'witnessed', effect: 'police_attention', target: 'world', delay: '1 day' },
                    { condition: 'family_member', effect: 'family_crisis', target: 'family', delay: '6 hours' }
                ]
            }
        },
        
        // Financial consequences
        'financial_actions': {
            'large_spending': {
                immediate: [
                    { condition: 'family_notices', effect: 'suspicion_increase', target: 'family' }
                ],
                delayed: [
                    { condition: 'public_display', effect: 'attention_increase', target: 'social', delay: '1 day' }
                ]
            },
            
            'bribery': {
                immediate: [
                    { condition: 'success', effect: 'corruption_unlock', target: 'world' }
                ],
                delayed: [
                    { condition: 'discovered', effect: 'police_investigation', target: 'world', delay: '3 days' }
                ]
            }
        },
        
        // Location-based consequences
        'location_actions': {
            'trespassing': {
                immediate: [
                    { condition: 'caught', effect: 'reputation_decrease', target: 'social' }
                ],
                delayed: [
                    { condition: 'repeated_offense', effect: 'police_attention', target: 'world', delay: '1 day' }
                ]
            },
            
            'public_corruption': {
                immediate: [
                    { condition: 'witnessed', effect: 'scandal_creation', target: 'social' }
                ],
                delayed: [
                    { condition: 'evidence_exists', effect: 'blackmail_vulnerability', target: 'player', delay: '2 days' }
                ]
            }
        }
    },
    
    // World state consequences
    world_consequences: {
        'police_attention': {
            levels: {
                1: { name: 'Noticed', effects: ['increased_scrutiny'] },
                2: { name: 'Suspicious', effects: ['random_checks', 'movement_tracking'] },
                3: { name: 'Under Investigation', effects: ['active_surveillance', 'asset_freezing'] },
                4: { name: 'Wanted', effects: ['arrest_warrant', 'game_over_risk'] }
            }
        },
        
        'family_crisis': {
            levels: {
                1: { name: 'Tension', effects: ['awkward_interactions', 'reduced_trust'] },
                2: { name: 'Conflict', effects: ['arguments', 'restricted_access'] },
                3: { name: 'Crisis', effects: ['family_meeting', 'ultimatum'] },
                4: { name: 'Breakdown', effects: ['kicked_out', 'family_lost'] }
            }
        },
        
        'social_scandal': {
            levels: {
                1: { name: 'Rumors', effects: ['gossip_spread', 'social_awkwardness'] },
                2: { name: 'Scandal', effects: ['social_isolation', 'opportunity_loss'] },
                3: { name: 'Infamy', effects: ['public_shaming', 'location_bans'] },
                4: { name: 'Pariah', effects: ['complete_isolation', 'forced_relocation'] }
            }
        }
    },
    
    // Recovery mechanisms
    recovery_options: {
        'reputation_repair': {
            'family': [
                { action: 'family_therapy', cost: 100000, reputation_gain: 20, time: '1 week' },
                { action: 'family_vacation', cost: 500000, reputation_gain: 30, time: '2 weeks' },
                { action: 'grand_gesture', cost: 1000000, reputation_gain: 50, time: '1 month' }
            ],
            
            'school': [
                { action: 'academic_excellence', cost: 0, reputation_gain: 15, time: '1 month' },
                { action: 'charity_work', cost: 50000, reputation_gain: 25, time: '2 weeks' },
                { action: 'school_donation', cost: 1000000, reputation_gain: 40, time: '1 week' }
            ],
            
            'social': [
                { action: 'community_service', cost: 0, reputation_gain: 10, time: '1 month' },
                { action: 'public_relations', cost: 200000, reputation_gain: 20, time: '2 weeks' },
                { action: 'media_campaign', cost: 1000000, reputation_gain: 35, time: '1 week' }
            ]
        },
        
        'crisis_management': {
            'police_attention': [
                { action: 'lawyer_consultation', cost: 500000, reduction: 1, time: '1 week' },
                { action: 'evidence_destruction', cost: 100000, reduction: 2, time: '3 days', risk: 'high' },
                { action: 'police_bribery', cost: 2000000, reduction: 3, time: '1 day', risk: 'extreme' }
            ],
            
            'family_crisis': [
                { action: 'family_counseling', cost: 200000, reduction: 1, time: '2 weeks' },
                { action: 'apology_gifts', cost: 500000, reduction: 2, time: '1 week' },
                { action: 'family_manipulation', cost: 0, reduction: 3, time: '3 days', risk: 'medium' }
            ]
        }
    }
};

/**
 * Consequence Manager - Handles action consequences and world responses
 */
export class ConsequenceManager {
    constructor(stateManager, gameEngine) {
        this.stateManager = stateManager;
        this.gameEngine = gameEngine;
        this.consequenceData = consequenceData;
        
        // Initialize consequence state
        this.initializeConsequenceState();
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    /**
     * Initialize consequence state
     */
    initializeConsequenceState() {
        const worldState = this.stateManager.state.world;
        
        if (!worldState.reputation) {
            worldState.reputation = {
                family: 0,
                school: 0,
                social: 0
            };
        }
        
        if (!worldState.consequences) {
            worldState.consequences = [];
        }
        
        if (!worldState.crisis_levels) {
            worldState.crisis_levels = {
                police_attention: 0,
                family_crisis: 0,
                social_scandal: 0
            };
        }
        
        if (!worldState.pending_consequences) {
            worldState.pending_consequences = [];
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for player actions
        this.gameEngine.on('player:interact', (data) => {
            this.processActionConsequences('character_interaction', data.action, data);
        });
        
        this.gameEngine.on('player:travel', (data) => {
            this.processLocationConsequences(data);
        });
        
        this.gameEngine.on('player:system', (data) => {
            this.processSystemConsequences(data);
        });
        
        // Listen for time changes to process delayed consequences
        this.gameEngine.on('world:new_day', () => {
            this.processDelayedConsequences();
        });
        
        // Listen for character changes
        this.gameEngine.on('character:corruption', (data) => {
            this.processCorruptionConsequences(data);
        });
    }
    
    /**
     * Process action consequences
     */
    processActionConsequences(actionCategory, actionType, actionData) {
        const consequences = this.consequenceData.action_consequences[actionCategory]?.[actionType];
        if (!consequences) return;
        
        // Process immediate consequences
        consequences.immediate?.forEach(consequence => {
            if (this.evaluateCondition(consequence.condition, actionData)) {
                this.applyConsequence(consequence, actionData);
            }
        });
        
        // Schedule delayed consequences
        consequences.delayed?.forEach(consequence => {
            if (this.evaluateCondition(consequence.condition, actionData)) {
                this.scheduleDelayedConsequence(consequence, actionData);
            }
        });
    }
    
    /**
     * Evaluate consequence condition
     */
    evaluateCondition(condition, actionData) {
        switch (condition) {
            case 'success':
                return actionData.result?.success === true;
            case 'failure':
                return actionData.result?.success === false;
            case 'public_location':
                return this.isPublicLocation(actionData.location);
            case 'family_member':
                return this.isFamilyMember(actionData.characterId);
            case 'witnessed':
                return this.wasWitnessed(actionData);
            case 'repeated_observation':
                return this.isRepeatedAction(actionData, 'observe');
            case 'family_notices':
                return this.familyNoticesSpending(actionData);
            default:
                return false;
        }
    }
    
    /**
     * Apply immediate consequence
     */
    applyConsequence(consequence, actionData) {
        const { effect, target } = consequence;
        
        switch (effect) {
            case 'suspicion_increase':
                this.increaseSuspicion(target, actionData);
                break;
            case 'lust_increase':
                this.increaseLust(target, actionData);
                break;
            case 'fear_increase':
                this.increaseFear(target, actionData);
                break;
            case 'reputation_decrease':
                this.decreaseReputation(target, 10);
                break;
            case 'scandal_creation':
                this.createScandal(actionData);
                break;
            default:
                console.log(`Unknown consequence effect: ${effect}`);
        }
        
        // Log consequence
        this.logConsequence(effect, target, actionData);
    }
    
    /**
     * Schedule delayed consequence
     */
    scheduleDelayedConsequence(consequence, actionData) {
        const worldState = this.stateManager.state.world;
        const delay = this.parseDelay(consequence.delay);
        
        worldState.pending_consequences.push({
            consequence,
            actionData,
            scheduled_time: Date.now() + delay,
            id: this.generateId()
        });
        
        this.stateManager.triggerAutoSave();
    }
    
    /**
     * Process delayed consequences
     */
    processDelayedConsequences() {
        const worldState = this.stateManager.state.world;
        const now = Date.now();
        
        const toProcess = worldState.pending_consequences.filter(pc => pc.scheduled_time <= now);
        
        toProcess.forEach(pendingConsequence => {
            this.applyConsequence(pendingConsequence.consequence, pendingConsequence.actionData);
            
            // Remove from pending
            const index = worldState.pending_consequences.indexOf(pendingConsequence);
            worldState.pending_consequences.splice(index, 1);
        });
        
        if (toProcess.length > 0) {
            this.stateManager.triggerAutoSave();
        }
    }
    
    /**
     * Increase character suspicion
     */
    increaseSuspicion(target, actionData) {
        if (target === 'target_character' && actionData.characterId) {
            const character = this.stateManager.getCharacter(actionData.characterId);
            if (character) {
                character.stats.suspicion = Math.min(100, character.stats.suspicion + 10);
                
                // Check for suspicion threshold consequences
                if (character.stats.suspicion > 70) {
                    this.triggerSuspicionConsequences(actionData.characterId);
                }
            }
        }
    }
    
    /**
     * Decrease reputation
     */
    decreaseReputation(category, amount) {
        const worldState = this.stateManager.state.world;
        
        if (worldState.reputation[category] !== undefined) {
            worldState.reputation[category] = Math.max(-100, worldState.reputation[category] - amount);
            
            // Check for reputation threshold consequences
            this.checkReputationConsequences(category);
        }
    }
    
    /**
     * Check reputation consequences
     */
    checkReputationConsequences(category) {
        const worldState = this.stateManager.state.world;
        const reputation = worldState.reputation[category];
        const categoryData = this.consequenceData.reputation_categories[category];
        
        if (!categoryData) return;
        
        // Find current reputation level
        let currentLevel = 'neutral';
        Object.entries(categoryData.effects).forEach(([level, data]) => {
            if (reputation <= data.threshold) {
                currentLevel = level;
            }
        });
        
        // Apply reputation effects
        const effects = categoryData.effects[currentLevel]?.effects || [];
        effects.forEach(effect => {
            this.applyReputationEffect(effect, category);
        });
    }
    
    /**
     * Apply reputation effect
     */
    applyReputationEffect(effect, category) {
        const worldState = this.stateManager.state.world;
        
        switch (effect) {
            case 'family_hostility':
                this.increaseCrisisLevel('family_crisis', 2);
                break;
            case 'kicked_out_risk':
                this.scheduleKickOutEvent();
                break;
            case 'police_attention':
                this.increaseCrisisLevel('police_attention', 1);
                break;
            case 'social_isolation':
                this.applySocialIsolation();
                break;
            default:
                console.log(`Unknown reputation effect: ${effect}`);
        }
    }
    
    /**
     * Increase crisis level
     */
    increaseCrisisLevel(crisisType, amount) {
        const worldState = this.stateManager.state.world;
        const oldLevel = worldState.crisis_levels[crisisType];
        worldState.crisis_levels[crisisType] = Math.min(4, oldLevel + amount);
        
        // Emit crisis level change event
        this.gameEngine.emit('world:crisis_level_change', {
            crisisType,
            oldLevel,
            newLevel: worldState.crisis_levels[crisisType]
        });
        
        // Check for crisis consequences
        this.checkCrisisConsequences(crisisType);
    }
    
    /**
     * Check crisis consequences
     */
    checkCrisisConsequences(crisisType) {
        const worldState = this.stateManager.state.world;
        const level = worldState.crisis_levels[crisisType];
        const crisisData = this.consequenceData.world_consequences[crisisType];
        
        if (!crisisData || !crisisData.levels[level]) return;
        
        const levelData = crisisData.levels[level];
        
        // Apply crisis effects
        levelData.effects.forEach(effect => {
            this.applyCrisisEffect(effect, crisisType, level);
        });
        
        // Emit crisis event
        this.gameEngine.emit('world:crisis_triggered', {
            crisisType,
            level,
            name: levelData.name,
            effects: levelData.effects
        });
    }
    
    /**
     * Apply crisis effect
     */
    applyCrisisEffect(effect, crisisType, level) {
        switch (effect) {
            case 'game_over_risk':
                this.triggerGameOverRisk();
                break;
            case 'kicked_out':
                this.triggerKickOut();
                break;
            case 'asset_freezing':
                this.freezeAssets();
                break;
            case 'family_lost':
                this.triggerFamilyLoss();
                break;
            default:
                console.log(`Unknown crisis effect: ${effect}`);
        }
    }
    
    /**
     * Helper methods for condition evaluation
     */
    isPublicLocation(location) {
        const publicLocations = ['School', 'Mall', 'Park', 'Temple', 'Market'];
        return publicLocations.includes(location);
    }
    
    isFamilyMember(characterId) {
        const familyMembers = ['sonia', 'natasha', 'tanya'];
        return familyMembers.includes(characterId);
    }
    
    wasWitnessed(actionData) {
        // Simple implementation - could be more complex
        return Math.random() < 0.3; // 30% chance of being witnessed
    }
    
    isRepeatedAction(actionData, actionType) {
        // Check if this action has been performed multiple times recently
        const recentActions = this.getRecentActions(actionData.characterId, actionType);
        return recentActions.length > 2;
    }
    
    familyNoticesSpending(actionData) {
        // Family notices large spending
        return actionData.amount > 100000; // 1 Lakh
    }
    
    /**
     * Utility methods
     */
    parseDelay(delayString) {
        const [amount, unit] = delayString.split(' ');
        const multipliers = {
            'minutes': 60 * 1000,
            'hours': 60 * 60 * 1000,
            'day': 24 * 60 * 60 * 1000,
            'days': 24 * 60 * 60 * 1000,
            'week': 7 * 24 * 60 * 60 * 1000,
            'weeks': 7 * 24 * 60 * 60 * 1000
        };
        
        return parseInt(amount) * (multipliers[unit] || 0);
    }
    
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    logConsequence(effect, target, actionData) {
        const worldState = this.stateManager.state.world;
        
        worldState.consequences.push({
            effect,
            target,
            actionData: {
                action: actionData.action,
                characterId: actionData.characterId,
                location: actionData.location
            },
            timestamp: Date.now()
        });
        
        // Keep only last 100 consequences
        if (worldState.consequences.length > 100) {
            worldState.consequences = worldState.consequences.slice(-100);
        }
    }
    
    getRecentActions(characterId, actionType) {
        const worldState = this.stateManager.state.world;
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
        
        return worldState.consequences.filter(c => 
            c.actionData.characterId === characterId &&
            c.actionData.action === actionType &&
            c.timestamp > oneDayAgo
        );
    }
    
    /**
     * Get consequence summary
     */
    getConsequenceSummary() {
        const worldState = this.stateManager.state.world;
        
        return {
            reputation: worldState.reputation,
            crisis_levels: worldState.crisis_levels,
            pending_consequences: worldState.pending_consequences.length,
            recent_consequences: worldState.consequences.slice(-10)
        };
    }
    
    /**
     * Crisis trigger methods (to be implemented based on game design)
     */
    triggerGameOverRisk() {
        this.gameEngine.emit('game:over_risk', { reason: 'police_attention' });
    }
    
    triggerKickOut() {
        this.gameEngine.emit('family:kick_out', { reason: 'family_crisis' });
    }
    
    freezeAssets() {
        const player = this.stateManager.getPlayer();
        player.assets_frozen = true;
    }
    
    triggerFamilyLoss() {
        this.gameEngine.emit('family:lost', { reason: 'complete_breakdown' });
    }
    
    scheduleKickOutEvent() {
        this.scheduleDelayedConsequence({
            effect: 'kicked_out',
            target: 'player',
            delay: '3 days'
        }, { reason: 'reputation' });
    }
    
    applySocialIsolation() {
        // Reduce access to social locations and characters
        const worldState = this.stateManager.state.world;
        worldState.social_isolation = true;
    }
    
    createScandal(actionData) {
        this.increaseCrisisLevel('social_scandal', 1);
    }
    
    increaseLust(target, actionData) {
        if (target === 'target_character' && actionData.characterId) {
            const character = this.stateManager.getCharacter(actionData.characterId);
            if (character) {
                character.stats.lust = Math.min(100, character.stats.lust + 5);
            }
        }
    }
    
    increaseFear(target, actionData) {
        if (target === 'target_character' && actionData.characterId) {
            const character = this.stateManager.getCharacter(actionData.characterId);
            if (character) {
                character.stats.fear = Math.min(100, character.stats.fear + 10);
            }
        }
    }
    
    triggerSuspicionConsequences(characterId) {
        // High suspicion consequences
        this.gameEngine.emit('character:high_suspicion', { characterId });
    }
    
    processLocationConsequences(data) {
        // Process consequences for location changes
        if (this.isPublicLocation(data.destination)) {
            // Public location consequences
        }
    }
    
    processSystemConsequences(data) {
        // Process consequences for system usage
        if (data.action === 'open' && Math.random() < 0.1) {
            // 10% chance family notices system usage
            this.decreaseReputation('family', 2);
        }
    }
    
    processCorruptionConsequences(data) {
        // Process consequences for character corruption
        if (data.corruption_level > 50) {
            // High corruption consequences
            this.gameEngine.emit('character:high_corruption', data);
        }
    }
}
