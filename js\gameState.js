// Auto-Recovery Game State Manager
// Automatically saves and recovers player progress

import { gameErrorHandler } from './errorHandler.js';

export class GameStateManager {
    constructor() {
        this.saveKey = 'predator_ascent_save';
        this.autoSaveInterval = 30000; // Auto-save every 30 seconds
        this.autoSaveTimer = null;
        this.lastSaveTime = 0;
        this.setupAutoSave();
        this.setupBeforeUnload();
    }

    setupAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveTimer = setInterval(() => {
            this.autoSave();
        }, this.autoSaveInterval);
    }

    setupBeforeUnload() {
        // Save before user closes the tab
        window.addEventListener('beforeunload', () => {
            this.save();
        });

        // Save when page becomes hidden (mobile/tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.save();
            }
        });
    }

    save() {
        try {
            const gameState = {
                player: window.player,
                currentChapter: window.main?.currentChapterNumber || 1,
                previousChapter: window.main?.previousChapterNumber || 1,
                timestamp: Date.now(),
                version: '1.0'
            };

            localStorage.setItem(this.saveKey, JSON.stringify(gameState));
            this.lastSaveTime = Date.now();
            console.log('Game saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            gameErrorHandler.handleError('save-error', error.message);
            return false;
        }
    }

    autoSave() {
        // Only auto-save if enough time has passed and player has made progress
        const timeSinceLastSave = Date.now() - this.lastSaveTime;
        if (timeSinceLastSave > 25000) { // At least 25 seconds since last save
            this.save();
        }
    }

    load() {
        try {
            const savedData = localStorage.getItem(this.saveKey);
            if (!savedData) {
                return null; // No save data found
            }

            const gameState = JSON.parse(savedData);
            
            // Validate save data
            if (!this.validateSaveData(gameState)) {
                console.warn('Save data appears corrupted, starting fresh');
                return null;
            }

            return gameState;
        } catch (error) {
            console.error('Failed to load save data:', error);
            gameErrorHandler.handleError('loading', 'Corrupted save data detected');
            return null;
        }
    }

    validateSaveData(gameState) {
        // Check if save data has required structure
        return gameState && 
               gameState.player && 
               gameState.currentChapter && 
               typeof gameState.currentChapter === 'number' &&
               gameState.timestamp &&
               gameState.version;
    }

    restore(gameState) {
        try {
            // Restore player data
            if (gameState.player) {
                Object.assign(window.player, gameState.player);
            }

            // Restore chapter progress
            if (gameState.currentChapter && window.main) {
                window.main.currentChapterNumber = gameState.currentChapter;
                window.main.previousChapterNumber = gameState.previousChapter || 1;
            }

            console.log('Game state restored successfully');
            return true;
        } catch (error) {
            console.error('Failed to restore game state:', error);
            gameErrorHandler.handleError('loading', 'Failed to restore save data');
            return false;
        }
    }

    getSaveInfo() {
        const savedData = this.load();
        if (!savedData) {
            return null;
        }

        return {
            chapter: savedData.currentChapter,
            timestamp: savedData.timestamp,
            timeAgo: this.getTimeAgo(savedData.timestamp),
            playerStats: savedData.player?.stats || {}
        };
    }

    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }

    deleteSave() {
        try {
            localStorage.removeItem(this.saveKey);
            console.log('Save data deleted');
            return true;
        } catch (error) {
            console.error('Failed to delete save:', error);
            return false;
        }
    }

    exportSave() {
        const gameState = this.load();
        if (!gameState) {
            gameErrorHandler.handleError('save-error', 'No save data to export');
            return null;
        }

        // Create downloadable save file
        const saveBlob = new Blob([JSON.stringify(gameState, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(saveBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `predator_ascent_save_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        gameErrorHandler.showSuccess('Save file downloaded! 💾');
        return true;
    }

    importSave(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const gameState = JSON.parse(e.target.result);
                    if (this.validateSaveData(gameState)) {
                        localStorage.setItem(this.saveKey, JSON.stringify(gameState));
                        gameErrorHandler.showSuccess('Save file imported! 🎮');
                        resolve(gameState);
                    } else {
                        reject(new Error('Invalid save file format'));
                    }
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    // Recovery methods for common issues
    recoverFromCorruption() {
        console.log('Attempting to recover from corrupted state...');
        
        // Try to restore from backup if available
        const backupKey = this.saveKey + '_backup';
        const backup = localStorage.getItem(backupKey);
        
        if (backup) {
            try {
                const backupState = JSON.parse(backup);
                if (this.validateSaveData(backupState)) {
                    localStorage.setItem(this.saveKey, backup);
                    gameErrorHandler.showSuccess('Recovered from backup! 🔧');
                    return backupState;
                }
            } catch (error) {
                console.error('Backup also corrupted:', error);
            }
        }

        // If no backup, reset to safe state
        this.resetToSafeState();
        return null;
    }

    resetToSafeState() {
        console.log('Resetting to safe state...');
        
        // Reset player to default state
        if (window.player) {
            window.player.stats = { cp: 0, sp: 0, dxp: 0 };
            window.player.inventory = [];
            window.player.skills = [];
            window.player.quests = [];
            window.player.location = "2BHK Flat, Malviya Nagar";
            window.player.time = "09:13 PM";
        }

        // Reset to chapter 1
        if (window.main) {
            window.main.currentChapterNumber = 1;
            window.main.previousChapterNumber = 1;
        }

        this.save();
        gameErrorHandler.showSuccess('Game reset to safe state 🔄');
    }

    createBackup() {
        try {
            const currentSave = localStorage.getItem(this.saveKey);
            if (currentSave) {
                localStorage.setItem(this.saveKey + '_backup', currentSave);
            }
        } catch (error) {
            console.error('Failed to create backup:', error);
        }
    }

    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
    }
}

// Create global instance
export const gameState = new GameStateManager();

// Make it globally accessible
window.gameState = gameState;
