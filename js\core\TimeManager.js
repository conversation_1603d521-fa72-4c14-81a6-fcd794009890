/**
 * TimeManager - Handles the world clock, calendar, and time-based mechanics
 * Core component of the Corruption Life Simulator
 */

export class TimeManager {
    constructor() {
        this.currentDay = 1;
        this.currentDayOfWeek = 'Monday'; // Monday = 0, Sunday = 6
        this.currentTimeSlot = 'Morning';
        this.currentHour = 7;
        this.currentMinute = 0;
        
        // Time slots and their hour ranges
        this.timeSlots = {
            'Morning': { start: 7, end: 8, next: 'School-P1' },
            'School-P1': { start: 8, end: 9, next: 'School-P2' },
            'School-P2': { start: 9, end: 10, next: 'School-P3' },
            'School-P3': { start: 10, end: 11, next: 'School-P4' },
            'School-P4': { start: 11, end: 12, next: 'Lunch' },
            'Lunch': { start: 12, end: 13, next: 'School-P5' },
            'School-P5': { start: 13, end: 14, next: 'Afternoon' },
            'Afternoon': { start: 14, end: 17, next: 'Evening' },
            'Evening': { start: 17, end: 20, next: 'Night' },
            'Night': { start: 20, end: 23, next: 'Late Night' },
            'Late Night': { start: 23, end: 7, next: 'Morning' }
        };
        
        this.daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        
        // Event listeners for time changes
        this.timeChangeListeners = [];
        this.dayChangeListeners = [];
    }
    
    /**
     * Get current time as formatted string
     */
    getCurrentTimeString() {
        const hour = this.currentHour.toString().padStart(2, '0');
        const minute = this.currentMinute.toString().padStart(2, '0');
        return `${hour}:${minute}`;
    }
    
    /**
     * Get current day info
     */
    getCurrentDayInfo() {
        return {
            day: this.currentDay,
            dayOfWeek: this.currentDayOfWeek,
            timeSlot: this.currentTimeSlot,
            time: this.getCurrentTimeString(),
            isWeekend: this.isWeekend(),
            isSchoolDay: this.isSchoolDay(),
            isSchoolHours: this.isSchoolHours()
        };
    }
    
    /**
     * Check if current day is weekend
     */
    isWeekend() {
        return this.currentDayOfWeek === 'Saturday' || this.currentDayOfWeek === 'Sunday';
    }
    
    /**
     * Check if current day is a school day
     */
    isSchoolDay() {
        return !this.isWeekend();
    }
    
    /**
     * Check if current time is during school hours
     */
    isSchoolHours() {
        return this.currentTimeSlot.startsWith('School') || this.currentTimeSlot === 'Lunch';
    }
    
    /**
     * Advance time by specified amount
     * @param {string} amount - 'minutes', 'hour', 'timeSlot', 'day'
     * @param {number} value - How much to advance
     */
    advanceTime(amount, value = 1) {
        const oldDay = this.currentDay;
        const oldTimeSlot = this.currentTimeSlot;
        
        switch (amount) {
            case 'minutes':
                this.advanceMinutes(value);
                break;
            case 'hour':
                this.advanceHours(value);
                break;
            case 'timeSlot':
                this.advanceTimeSlots(value);
                break;
            case 'day':
                this.advanceDays(value);
                break;
        }
        
        // Trigger events if day or time slot changed
        if (oldTimeSlot !== this.currentTimeSlot) {
            this.notifyTimeChange(oldTimeSlot, this.currentTimeSlot);
        }
        
        if (oldDay !== this.currentDay) {
            this.notifyDayChange(oldDay, this.currentDay);
        }
    }
    
    /**
     * Advance by minutes
     */
    advanceMinutes(minutes) {
        this.currentMinute += minutes;
        
        while (this.currentMinute >= 60) {
            this.currentMinute -= 60;
            this.currentHour++;
        }
        
        this.updateTimeSlot();
        this.updateDay();
    }
    
    /**
     * Advance by hours
     */
    advanceHours(hours) {
        this.currentHour += hours;
        this.updateTimeSlot();
        this.updateDay();
    }
    
    /**
     * Advance by time slots
     */
    advanceTimeSlots(slots) {
        for (let i = 0; i < slots; i++) {
            const currentSlot = this.timeSlots[this.currentTimeSlot];
            if (currentSlot && currentSlot.next) {
                this.currentTimeSlot = currentSlot.next;
                this.currentHour = this.timeSlots[this.currentTimeSlot].start;
                this.currentMinute = 0;
                
                // Check if we moved to next day
                if (this.currentTimeSlot === 'Morning' && i < slots - 1) {
                    this.advanceDays(1);
                }
            }
        }
    }
    
    /**
     * Advance by days
     */
    advanceDays(days) {
        this.currentDay += days;
        
        // Update day of week
        const currentDayIndex = this.daysOfWeek.indexOf(this.currentDayOfWeek);
        const newDayIndex = (currentDayIndex + days) % 7;
        this.currentDayOfWeek = this.daysOfWeek[newDayIndex];
        
        // Reset to morning
        this.currentTimeSlot = 'Morning';
        this.currentHour = 7;
        this.currentMinute = 0;
    }
    
    /**
     * Update time slot based on current hour
     */
    updateTimeSlot() {
        for (const [slot, info] of Object.entries(this.timeSlots)) {
            if (this.currentHour >= info.start && this.currentHour < info.end) {
                this.currentTimeSlot = slot;
                break;
            }
        }
    }
    
    /**
     * Update day if hour rolled over
     */
    updateDay() {
        while (this.currentHour >= 24) {
            this.currentHour -= 24;
            this.advanceDays(1);
        }
    }
    
    /**
     * Add listener for time slot changes
     */
    onTimeChange(callback) {
        this.timeChangeListeners.push(callback);
    }
    
    /**
     * Add listener for day changes
     */
    onDayChange(callback) {
        this.dayChangeListeners.push(callback);
    }
    
    /**
     * Notify listeners of time change
     */
    notifyTimeChange(oldTimeSlot, newTimeSlot) {
        this.timeChangeListeners.forEach(callback => {
            try {
                callback(oldTimeSlot, newTimeSlot, this.getCurrentDayInfo());
            } catch (error) {
                console.error('Error in time change listener:', error);
            }
        });
    }
    
    /**
     * Notify listeners of day change
     */
    notifyDayChange(oldDay, newDay) {
        this.dayChangeListeners.forEach(callback => {
            try {
                callback(oldDay, newDay, this.getCurrentDayInfo());
            } catch (error) {
                console.error('Error in day change listener:', error);
            }
        });
    }
    
    /**
     * Get time cost for various actions
     */
    getActionTimeCost(action) {
        const timeCosts = {
            'travel_home_to_school': { amount: 'minutes', value: 30 },
            'travel_school_to_home': { amount: 'minutes', value: 30 },
            'travel_home_to_mall': { amount: 'minutes', value: 45 },
            'travel_mall_to_home': { amount: 'minutes', value: 45 },
            'study': { amount: 'hour', value: 1 },
            'interact_brief': { amount: 'minutes', value: 15 },
            'interact_extended': { amount: 'minutes', value: 45 },
            'quest_step': { amount: 'minutes', value: 30 },
            'shop': { amount: 'minutes', value: 20 },
            'system_menu': { amount: 'minutes', value: 5 }
        };
        
        return timeCosts[action] || { amount: 'minutes', value: 10 };
    }
    
    /**
     * Execute action with time cost
     */
    executeAction(action, callback) {
        const timeCost = this.getActionTimeCost(action);
        this.advanceTime(timeCost.amount, timeCost.value);
        
        if (callback) {
            callback(this.getCurrentDayInfo());
        }
    }
    
    /**
     * Save time state
     */
    saveState() {
        return {
            currentDay: this.currentDay,
            currentDayOfWeek: this.currentDayOfWeek,
            currentTimeSlot: this.currentTimeSlot,
            currentHour: this.currentHour,
            currentMinute: this.currentMinute
        };
    }
    
    /**
     * Load time state
     */
    loadState(state) {
        if (state) {
            this.currentDay = state.currentDay || 1;
            this.currentDayOfWeek = state.currentDayOfWeek || 'Monday';
            this.currentTimeSlot = state.currentTimeSlot || 'Morning';
            this.currentHour = state.currentHour || 7;
            this.currentMinute = state.currentMinute || 0;
        }
    }
}
