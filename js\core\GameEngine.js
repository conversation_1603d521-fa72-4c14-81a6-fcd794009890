/**
 * GameEngine - Central event dispatcher and game logic coordinator
 * Handles all game events and coordinates between different systems
 */

import { QuestManager } from '../data/quests.js';

export class GameEngine {
    constructor(timeManager, stateManager, scheduleManager) {
        this.timeManager = timeManager;
        this.stateManager = stateManager;
        this.scheduleManager = scheduleManager;

        // Initialize quest manager
        this.questManager = new QuestManager(stateManager, this);

        // Event listeners
        this.eventListeners = {};

        // Game state
        this.isInitialized = false;
        this.isPaused = false;

        // Setup core event listeners
        this.setupCoreListeners();
    }
    
    /**
     * Initialize the game engine
     */
    initialize() {
        if (this.isInitialized) return;
        
        console.log('Initializing Game Engine...');
        
        // Setup time-based events
        this.timeManager.onTimeChange((oldSlot, newSlot, dayInfo) => {
            this.handleTimeChange(oldSlot, newSlot, dayInfo);
        });
        
        this.timeManager.onDayChange((oldDay, newDay, dayInfo) => {
            this.handleDayChange(oldDay, newDay, dayInfo);
        });
        
        // Initialize quest system
        this.questManager.initialize();

        this.isInitialized = true;
        this.emit('engine:initialized');

        console.log('Game Engine initialized successfully');
    }
    
    /**
     * Setup core event listeners
     */
    setupCoreListeners() {
        // Player actions
        this.on('player:travel', (data) => this.handlePlayerTravel(data));
        this.on('player:interact', (data) => this.handlePlayerInteraction(data));
        this.on('player:quest', (data) => this.handleQuestAction(data));
        this.on('player:shop', (data) => this.handleShopAction(data));
        this.on('player:system', (data) => this.handleSystemAction(data));
        
        // Character events
        this.on('character:corruption', (data) => this.handleCharacterCorruption(data));
        this.on('character:loyalty', (data) => this.handleLoyaltyChange(data));
        this.on('character:conquest', (data) => this.handleCharacterConquest(data));
        
        // World events
        this.on('world:location_unlock', (data) => this.handleLocationUnlock(data));
        this.on('world:rumor', (data) => this.handleRumor(data));
        this.on('world:consequence', (data) => this.handleConsequence(data));
    }
    
    /**
     * Add event listener
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }
    
    /**
     * Remove event listener
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(callback);
            if (index > -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }
    
    /**
     * Emit event
     */
    emit(event, data = {}) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * Handle time slot changes
     */
    handleTimeChange(oldSlot, newSlot, dayInfo) {
        console.log(`Time changed: ${oldSlot} -> ${newSlot}`);
        
        // Update character locations based on schedules
        this.scheduleManager.updateCharacterLocations(dayInfo);
        
        // Check for time-based events
        this.checkTimeBasedEvents(newSlot, dayInfo);
        
        // Update UI
        this.emit('ui:time_update', dayInfo);
        
        // Trigger auto-save
        this.stateManager.triggerAutoSave();
    }
    
    /**
     * Handle day changes
     */
    handleDayChange(oldDay, newDay, dayInfo) {
        console.log(`Day changed: ${oldDay} -> ${newDay}`);
        
        // Generate new daily objectives
        this.generateDailyObjectives();
        
        // Process harem loyalty decay
        this.processHaremMaintenance();
        
        // Update character schedules
        this.scheduleManager.generateDailySchedules(dayInfo);
        
        // Emit day change event
        this.emit('world:new_day', { oldDay, newDay, dayInfo });
    }
    
    /**
     * Handle player travel
     */
    handlePlayerTravel(data) {
        const { destination, origin } = data;
        const player = this.stateManager.getPlayer();
        
        // Calculate travel time
        const travelAction = `travel_${origin.toLowerCase()}_to_${destination.toLowerCase()}`;
        const timeCost = this.timeManager.getActionTimeCost(travelAction);
        
        // Execute travel
        this.timeManager.advanceTime(timeCost.amount, timeCost.value);
        
        // Update player location
        player.location = destination;
        
        // Check for travel events
        this.checkLocationEvents(destination);
        
        this.emit('ui:location_update', { location: destination });
    }
    
    /**
     * Handle player interactions
     */
    handlePlayerInteraction(data) {
        const { characterId, action, options = {} } = data;
        const character = this.stateManager.getCharacter(characterId);
        
        if (!character) {
            console.error(`Character not found: ${characterId}`);
            return;
        }
        
        // Process interaction based on action type
        const result = this.processInteraction(character, action, options);
        
        // Advance time
        const timeCost = options.extended ? 'interact_extended' : 'interact_brief';
        this.timeManager.executeAction(timeCost);
        
        // Emit interaction result
        this.emit('ui:interaction_result', { characterId, action, result });
    }
    
    /**
     * Process character interaction
     */
    processInteraction(character, action, options) {
        const player = this.stateManager.getPlayer();
        const result = { success: false, message: '', effects: {} };
        
        switch (action) {
            case 'observe':
                result.success = true;
                result.message = `You carefully observe ${character.name}...`;
                // Gain intel about character
                break;
                
            case 'talk':
                result.success = true;
                result.message = `You engage ${character.name} in conversation...`;
                // Improve relationship slightly
                this.stateManager.updateCharacterStats(character.name.toLowerCase().replace(' ', ''), {
                    love: 1
                });
                break;
                
            case 'intimidate':
                const intimidateSuccess = player.stats.dxp > character.stats.fear;
                result.success = intimidateSuccess;
                if (intimidateSuccess) {
                    result.message = `${character.name} is clearly intimidated by your presence...`;
                    this.stateManager.updateCharacterStats(character.name.toLowerCase().replace(' ', ''), {
                        fear: 5,
                        loyalty: -2
                    });
                } else {
                    result.message = `${character.name} doesn't seem impressed by your attempt...`;
                }
                break;
                
            case 'seduce':
                const seduceSuccess = player.stats.cp > character.stats.corruption;
                result.success = seduceSuccess;
                if (seduceSuccess) {
                    result.message = `${character.name} responds positively to your advances...`;
                    this.stateManager.updateCharacterStats(character.name.toLowerCase().replace(' ', ''), {
                        lust: 3,
                        corruption: 1
                    });
                } else {
                    result.message = `${character.name} rejects your advances...`;
                    this.stateManager.updateCharacterStats(character.name.toLowerCase().replace(' ', ''), {
                        suspicion: 2
                    });
                }
                break;
        }
        
        return result;
    }
    
    /**
     * Generate daily objectives
     */
    generateDailyObjectives() {
        const dailyQuests = [
            {
                title: 'Social Predator',
                description: 'Successfully interact with 3 different characters',
                reward: { cp: 20, sp: 1 },
                progress: 0,
                target: 3
            },
            {
                title: 'System Mastery',
                description: 'Use the System Shop or Apps 2 times',
                reward: { cp: 15, sp: 1 },
                progress: 0,
                target: 2
            },
            {
                title: 'Intelligence Gathering',
                description: 'Observe or investigate 2 characters',
                reward: { cp: 25, dxp: 5 },
                progress: 0,
                target: 2
            }
        ];
        
        // Clear existing daily quests
        this.stateManager.state.quests.daily = [];
        
        // Add new daily quests
        dailyQuests.forEach(quest => {
            this.stateManager.addQuest('daily', quest);
        });
    }
    
    /**
     * Process harem maintenance
     */
    processHaremMaintenance() {
        const harem = this.stateManager.state.harem;
        const decayRate = harem.loyalty_decay_rate;
        
        harem.members.forEach(memberId => {
            const character = this.stateManager.getCharacter(memberId);
            if (character && character.conquered) {
                // Decay loyalty over time
                this.stateManager.updateCharacterStats(memberId, {
                    loyalty: -decayRate
                });
                
                // Check for rebellion risk
                if (character.stats.loyalty < 20) {
                    this.emit('harem:rebellion_risk', { characterId: memberId });
                }
            }
        });
    }
    
    /**
     * Check for time-based events
     */
    checkTimeBasedEvents(timeSlot, dayInfo) {
        // School-specific events
        if (timeSlot.startsWith('School') && dayInfo.isSchoolDay) {
            this.checkSchoolEvents(timeSlot, dayInfo);
        }
        
        // Evening events
        if (timeSlot === 'Evening') {
            this.checkEveningEvents(dayInfo);
        }
        
        // Night events (prime corruption time)
        if (timeSlot === 'Night' || timeSlot === 'Late Night') {
            this.checkNightEvents(dayInfo);
        }
    }
    
    /**
     * Check school events
     */
    checkSchoolEvents(timeSlot, dayInfo) {
        // Generate random school encounters
        if (Math.random() < 0.3) { // 30% chance
            this.emit('event:school_encounter', { timeSlot, dayInfo });
        }
    }
    
    /**
     * Check evening events
     */
    checkEveningEvents(dayInfo) {
        // Family dinner events, etc.
        this.emit('event:evening_routine', { dayInfo });
    }
    
    /**
     * Check night events
     */
    checkNightEvents(dayInfo) {
        // Prime time for corruption activities
        this.emit('event:night_opportunities', { dayInfo });
    }
    
    /**
     * Check location-specific events
     */
    checkLocationEvents(location) {
        // Generate location-specific opportunities
        const events = {
            'School': ['classroom_encounter', 'hallway_gossip', 'teacher_opportunity'],
            'Mall': ['shopping_encounter', 'stalking_opportunity', 'public_corruption'],
            'Home': ['family_interaction', 'privacy_opportunity', 'system_access']
        };
        
        const locationEvents = events[location] || [];
        if (locationEvents.length > 0 && Math.random() < 0.4) {
            const randomEvent = locationEvents[Math.floor(Math.random() * locationEvents.length)];
            this.emit(`event:${randomEvent}`, { location });
        }
    }
    
    /**
     * Pause game engine
     */
    pause() {
        this.isPaused = true;
    }
    
    /**
     * Resume game engine
     */
    resume() {
        this.isPaused = false;
    }
    
    /**
     * Get current game state summary
     */
    getGameState() {
        return {
            time: this.timeManager.getCurrentDayInfo(),
            player: this.stateManager.getPlayer(),
            characters: this.stateManager.getAllCharacters(),
            isPaused: this.isPaused
        };
    }
}
