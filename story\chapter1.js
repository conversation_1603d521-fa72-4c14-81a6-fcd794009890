export const chapterData = {
    "start": {
        "text": `
            <p>The pain in your leg is a dull, constant throb, a phantom echo of the crash. It’s been ten days since the funeral. Ten days of living in this small, suffocating flat that smells of Dettol, incense sticks, and your mother’s quiet, unending grief.</p>
            <p>You lie on your bed, staring at the peeling paint on the ceiling. The whirring of the old Usha fan does little to cut through the oppressive late-August humidity. <em>Normal sounds. A normal, shitty life.</em></p>
            <p>Except for the thing that lives in your head now.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Available]</div>
                You've ignored these for days, writing them off as trauma-induced hallucinations. But the threat of another splitting headache forces your hand.
            </div>
        `,
        "choices": [
            { "label": "Claim the Bonus.", "leadsTo": "choice1_result" }
        ]
    },
    "choice1_result": {
        "onLoad": function() {
            window.player.stats.cp += 10;
            window.player.stats.sp += 1;
        },
        "text": `
            <p>With a sense of weary resignation, you focus your intent. <em>Fine. Yes. Claim.</em></p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Claimed!]</div>
                <span class="reward">[+10 Corruption Points (CP) added.]</span><br>
                <span class="reward">[+1 System Point (SP) added.]</span>
            </div>
            <p>A strange, faint warmth spreads through your chest. It's immediately followed by a new, more insistent prompt.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Initiated: The World Through New Eyes]</div>
                <strong>Quest 1: Know Your Assets.</strong><br>
                <strong>Objective:</strong> View the profiles of the three female occupants of this dwelling: <span class="character-name" onclick="window.ui.showModal('sonia')">Sonia</span>, <span class="character-name" onclick="window.ui.showModal('natasha')">Natasha</span>, and <span class="character-name" onclick="window.ui.showModal('tanya')">Tanya</span>.<br>
                <span class="reward"><strong>Reward:</strong> +50 CP, +2 SP, Skill Unlocked: [Mental Resonance Scan (Lvl 1)].</span><br>
                <span class="penalty"><strong>Failure Penalty:</strong> Persistent Migraine for 24 hours.</span>
            </div>
        `,
        "choices": [
            { "label": "Accept the Quest. View the Profiles.", "leadsTo": "choice2_result" }
        ]
    },
    "choice2_result": {
        "onLoad": function() {
            window.player.stats.cp += 50;
            window.player.stats.sp += 2;
            window.player.skills.push("Mental Resonance Scan (Lvl 1)");
        },
        "text": `
            <p>You accept. The system complies instantly. You click on their names, and the truth is laid bare.</p>
            <div class="system-prompt">
                <div class="prompt-title">[QUEST COMPLETE: Know Your Assets]</div>
                <span class="reward">[+50 CP, +2 SP received.]</span><br>
                <span class="reward">[Skill Unlocked: [Mental Resonance Scan (Lvl 1)]]</span>
            </div>
            <p>You now see them not as family, but as a collection of vulnerabilities. The knowledge is disgusting, and it gives you a terrifying sense of <strong>power</strong>.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Updated]</div>
                <strong>Quest 2: The First Tool.</strong><br>
                <strong>Objective:</strong> Open the [System Shop] and purchase your first item.
            </div>
        `,
        "choices": [
            { "label": "Open the System Shop.", "leadsTo": "choice3_result" }
        ]
    },
    "choice3_result": {
        "text": `
             <p>The Shop menu opens in your mind's eye. You have 60 CP to spend.</p>
             <p>Under [Consumables], you see a list:</p>
             <ul><li>[Sleeping Pills (Mild)] - 15 CP</li><li>[Laxative Powder (Odorless)] - 20 CP</li><li>[Aphrodisiac (Low Grade)] - 50 CP</li></ul>
             <p>Control is more important than immediate gratification.</p>
        `,
        "choices": [
            { "label": "Purchase [Sleeping Pills (Mild)] x2 for 30 CP.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.stats.cp -= 30;
            window.player.inventory.push({ item: "Sleeping Pills (Mild)", quantity: 2 });
        },
        "text": `
            <div class="system-prompt">
                <div class="prompt-title">[Purchase Confirmed!]</div>
                [-30 CP. Remaining Balance: 30 CP]<br>
                [Item has been materialized in your bedside drawer.]<br>
                [QUEST COMPLETE: The First Tool]
            </div>
            <p>You open your bedside drawer. There they are. Two small, unmarked pills in a tiny ziplock bag. They are real. The system is real. The power is real.</p>
            <hr>
        `,
        "choices": [
            { "label": "Proceed to Chapter 2", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 2 }
        ]
    }
};

export const modals = {
    "sonia": {"title": "[Character Profile: Sonia Singh]","content": `<p><strong>Designation:</strong> Mother. Primary Asset (Potential).</p><p><strong>Age:</strong> 42</p><p><strong>Status:</strong> Widowed. Financially Stressed (Perceived). <strong>Sexually Starved (Critical).</strong></p><p><strong>Asset Evaluation:</strong> Prime MILF Physique. Heavy, sagging breasts (38D), wide birthing hips (40), soft belly. Years of sexual neglect have created a deep-seated vulnerability. Prime for corruption.</p>`},
    "natasha": {"title": "[Character Profile: Natasha Singh]","content": `<p><strong>Designation:</strong> Sister. Secondary Asset.</p><p><strong>Age:</strong> 24</p><p><strong>Status:</strong> Underpaid HR Executive. Frustrated with life. Desperate for male validation and a wealthier lifestyle.</p><p><strong>Asset Evaluation:</strong> Voluptuous, top-heavy build (36D). Projects a 'sanskari' image but possesses a hidden slutty streak.</p>`},
    "tanya": {"title": "[Character Profile: Tanya Singh]","content": `<p><strong>Designation:</strong> Sister. Tertiary Asset.</p><p><strong>Age:</strong> 22</p><p><strong>Status:</strong> Underpaid HR Assistant. Fiery temper. Deep-seated inferiority complex masked by aggression.</p><p><strong>Asset Evaluation:</strong> Petite, athletic frame (32B). A 'chota packet, bada dhamaka'. Responds to dominance.</p>`}
};