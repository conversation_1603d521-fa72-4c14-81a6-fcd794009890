/**
 * SystemOS - The System Operating System Interface
 * Provides the player with apps for managing their corruption empire
 */

export class SystemOS {
    constructor(stateManager, gameEngine) {
        this.stateManager = stateManager;
        this.gameEngine = gameEngine;
        this.isOpen = false;
        this.currentApp = null;
        
        // App registry
        this.apps = {
            'contacts': { name: 'Contacts', icon: '👥', unlocked: true },
            'map': { name: 'Map', icon: '🗺️', unlocked: true },
            'bank': { name: 'Bank', icon: '💰', unlocked: true },
            'objectives': { name: 'Objectives', icon: '📋', unlocked: true },
            'shop': { name: 'Shop', icon: '🛒', unlocked: true },
            'harem': { name: 'Harem', icon: '👑', unlocked: this.checkHaremUnlock() },
            'intel': { name: 'Intel', icon: '🕵️', unlocked: false },
            'influence': { name: 'Influence', icon: '⚡', unlocked: false }
        };
        
        this.setupEventListeners();
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for system button clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('system-button') || 
                e.target.closest('.system-button')) {
                this.toggle();
            }
        });
        
        // Listen for escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * Toggle System OS
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    /**
     * Open System OS
     */
    open() {
        if (this.isOpen) return;
        
        this.isOpen = true;
        this.renderSystemOS();
        
        // Emit system access event
        this.gameEngine.emit('player:system', { action: 'open' });
    }
    
    /**
     * Close System OS
     */
    close() {
        if (!this.isOpen) return;
        
        this.isOpen = false;
        this.currentApp = null;
        
        const overlay = document.getElementById('system-overlay');
        if (overlay) {
            overlay.remove();
        }
        
        // Emit system close event
        this.gameEngine.emit('player:system', { action: 'close' });
    }
    
    /**
     * Render System OS interface
     */
    renderSystemOS() {
        // Remove existing overlay
        const existingOverlay = document.getElementById('system-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
        
        // Create overlay
        const overlay = document.createElement('div');
        overlay.id = 'system-overlay';
        overlay.className = 'system-overlay';
        
        overlay.innerHTML = `
            <div class="system-desktop">
                <div class="system-header">
                    <div class="system-title">[CORRUPTION SYSTEM OS v2.0]</div>
                    <div class="system-close" onclick="window.systemOS.close()">✕</div>
                </div>
                
                <div class="system-content">
                    <div class="system-dock">
                        ${this.renderAppDock()}
                    </div>
                    
                    <div class="system-app-area" id="system-app-area">
                        ${this.renderWelcomeScreen()}
                    </div>
                </div>
                
                <div class="system-status">
                    <div class="status-item">CP: ${this.stateManager.getPlayer().stats.cp}</div>
                    <div class="status-item">SP: ${this.stateManager.getPlayer().stats.sp}</div>
                    <div class="status-item">Wealth: ₹${this.formatCurrency(this.stateManager.getPlayer().stats.wealth)}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Add click handlers for apps
        this.setupAppClickHandlers();
    }
    
    /**
     * Render app dock
     */
    renderAppDock() {
        return Object.entries(this.apps)
            .filter(([id, app]) => app.unlocked)
            .map(([id, app]) => `
                <div class="system-app-icon ${this.currentApp === id ? 'active' : ''}" 
                     data-app="${id}" title="${app.name}">
                    <div class="app-icon">${app.icon}</div>
                    <div class="app-name">${app.name}</div>
                </div>
            `).join('');
    }
    
    /**
     * Render welcome screen
     */
    renderWelcomeScreen() {
        const player = this.stateManager.getPlayer();
        const timeInfo = this.gameEngine.timeManager.getCurrentDayInfo();
        
        return `
            <div class="welcome-screen">
                <h2>Welcome to the Corruption System</h2>
                <div class="system-stats">
                    <div class="stat-card">
                        <h3>Current Status</h3>
                        <p><strong>Day:</strong> ${timeInfo.day} (${timeInfo.dayOfWeek})</p>
                        <p><strong>Time:</strong> ${timeInfo.time} - ${timeInfo.timeSlot}</p>
                        <p><strong>Location:</strong> ${player.location}</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Power Level</h3>
                        <p><strong>Corruption Points:</strong> ${player.stats.cp}</p>
                        <p><strong>System Points:</strong> ${player.stats.sp}</p>
                        <p><strong>Dominance XP:</strong> ${player.stats.dxp}</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Empire Status</h3>
                        <p><strong>Conquered:</strong> ${this.stateManager.state.harem.members.length}</p>
                        <p><strong>Active Quests:</strong> ${this.getTotalActiveQuests()}</p>
                        <p><strong>Wealth:</strong> ₹${this.formatCurrency(player.stats.wealth)}</p>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <button class="system-btn" onclick="window.systemOS.openApp('objectives')">View Objectives</button>
                    <button class="system-btn" onclick="window.systemOS.openApp('contacts')">Character Profiles</button>
                    <button class="system-btn" onclick="window.systemOS.openApp('shop')">System Shop</button>
                </div>
            </div>
        `;
    }
    
    /**
     * Setup app click handlers
     */
    setupAppClickHandlers() {
        document.querySelectorAll('.system-app-icon').forEach(icon => {
            icon.addEventListener('click', () => {
                const appId = icon.dataset.app;
                this.openApp(appId);
            });
        });
    }
    
    /**
     * Open specific app
     */
    openApp(appId) {
        if (!this.apps[appId] || !this.apps[appId].unlocked) {
            console.warn(`App ${appId} is not available`);
            return;
        }
        
        this.currentApp = appId;
        
        // Update dock to show active app
        document.querySelectorAll('.system-app-icon').forEach(icon => {
            icon.classList.remove('active');
            if (icon.dataset.app === appId) {
                icon.classList.add('active');
            }
        });
        
        // Render app content
        const appArea = document.getElementById('system-app-area');
        if (appArea) {
            appArea.innerHTML = this.renderApp(appId);
        }
        
        // Emit app open event
        this.gameEngine.emit('player:system', { action: 'open_app', app: appId });
    }
    
    /**
     * Render specific app
     */
    renderApp(appId) {
        switch (appId) {
            case 'contacts':
                return this.renderContactsApp();
            case 'map':
                return this.renderMapApp();
            case 'bank':
                return this.renderBankApp();
            case 'objectives':
                return this.renderObjectivesApp();
            case 'shop':
                return this.renderShopApp();
            case 'harem':
                return this.renderHaremApp();
            default:
                return `<div class="app-placeholder">App "${appId}" is under development</div>`;
        }
    }

    /**
     * Check if harem app should be unlocked
     */
    checkHaremUnlock() {
        const harem = this.stateManager.state.harem;
        return harem.members.length > 0;
    }

    /**
     * Render Harem app
     */
    renderHaremApp() {
        const haremManager = window.haremManager;
        if (!haremManager) {
            return `<div class="app-error">Harem Manager not available</div>`;
        }

        const summary = haremManager.getHaremSummary();
        const harem = this.stateManager.state.harem;

        if (summary.total_members === 0) {
            return `
                <div class="harem-app">
                    <h2>👑 Harem Management</h2>
                    <div class="empty-harem">
                        <h3>No Conquered Assets</h3>
                        <p>You haven't conquered any characters yet.</p>
                        <p>Complete corruption paths to add members to your harem.</p>
                    </div>
                </div>
            `;
        }

        return `
            <div class="harem-app">
                <h2>👑 Harem Management</h2>

                <div class="harem-overview">
                    <div class="overview-stats">
                        <div class="stat-card">
                            <h3>Total Members</h3>
                            <div class="stat-value">${summary.total_members}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Active Abilities</h3>
                            <div class="stat-value">${summary.total_abilities}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Rebellion Risks</h3>
                            <div class="stat-value ${summary.rebellion_risks > 0 ? 'warning' : ''}">${summary.rebellion_risks}</div>
                        </div>
                    </div>
                </div>

                <div class="harem-members">
                    <h3>Harem Members</h3>
                    <div class="members-grid">
                        ${summary.members.map(member => `
                            <div class="harem-member ${member.rebellion_risk > 0 ? 'at-risk' : ''}">
                                <div class="member-header">
                                    <h4>${member.name}</h4>
                                    <div class="member-status">
                                        ${member.rebellion_risk > 0 ? '⚠️' : '✅'}
                                    </div>
                                </div>

                                <div class="member-stats">
                                    <div class="stat-bar">
                                        <label>Loyalty:</label>
                                        <div class="bar">
                                            <div class="fill loyalty" style="width: ${member.loyalty}%"></div>
                                        </div>
                                        <span>${member.loyalty}</span>
                                    </div>
                                    <div class="stat-bar">
                                        <label>Corruption:</label>
                                        <div class="bar">
                                            <div class="fill corruption" style="width: ${member.corruption}%"></div>
                                        </div>
                                        <span>${member.corruption}</span>
                                    </div>
                                </div>

                                <div class="member-info">
                                    <p><strong>Abilities:</strong> ${member.abilities}</p>
                                    <p><strong>Risk Level:</strong> ${this.getRiskLevelText(member.rebellion_risk)}</p>
                                </div>

                                <div class="member-actions">
                                    <button class="harem-btn" onclick="window.systemOS.showMemberDetails('${member.id}')">
                                        Manage
                                    </button>
                                    <button class="harem-btn maintenance" onclick="window.systemOS.showMaintenanceOptions('${member.id}')">
                                        Maintenance
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="harem-maintenance">
                    <h3>Maintenance Schedule</h3>
                    <p>Last maintenance check: ${harem.last_maintenance ? new Date(harem.last_maintenance).toLocaleDateString() : 'Never'}</p>
                    <button class="harem-btn" onclick="window.systemOS.performGlobalMaintenance()">
                        Perform Global Maintenance
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Get risk level text
     */
    getRiskLevelText(level) {
        switch (level) {
            case 0: return 'None';
            case 1: return 'Low';
            case 2: return 'Medium';
            case 3: return 'High';
            default: return 'Critical';
        }
    }

    /**
     * Show member details
     */
    showMemberDetails(characterId) {
        const character = this.stateManager.getCharacter(characterId);
        const haremManager = window.haremManager;
        const harem = this.stateManager.state.harem;
        const abilities = harem.abilities_unlocked[characterId] || [];

        const appArea = document.getElementById('system-app-area');
        appArea.innerHTML = `
            <div class="member-details">
                <h2>👑 ${character.name} - Harem Management</h2>

                <div class="member-overview">
                    <div class="member-stats-detailed">
                        ${Object.entries(character.stats).map(([stat, value]) => `
                            <div class="stat-bar">
                                <label>${stat.charAt(0).toUpperCase() + stat.slice(1)}:</label>
                                <div class="bar">
                                    <div class="fill ${stat}" style="width: ${value}%"></div>
                                </div>
                                <span>${value}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="member-abilities">
                    <h3>Unlocked Abilities</h3>
                    ${abilities.length > 0 ? `
                        <div class="abilities-list">
                            ${abilities.map(abilityId => {
                                const abilityData = haremManager.haremData.character_abilities[characterId]?.[abilityId];
                                return abilityData ? `
                                    <div class="ability-item">
                                        <h4>${abilityData.name}</h4>
                                        <p>${abilityData.description}</p>
                                        <div class="ability-effects">
                                            Effects: ${abilityData.effects.join(', ')}
                                        </div>
                                    </div>
                                ` : '';
                            }).join('')}
                        </div>
                    ` : '<p>No abilities unlocked yet.</p>'}
                </div>

                <div class="member-actions-detailed">
                    <button class="action-btn" onclick="window.systemOS.showMaintenanceOptions('${characterId}')">
                        Perform Maintenance
                    </button>
                    <button class="action-btn" onclick="window.systemOS.openApp('harem')">
                        ← Back to Harem
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Show maintenance options
     */
    showMaintenanceOptions(characterId) {
        const character = this.stateManager.getCharacter(characterId);
        const haremManager = window.haremManager;
        const activities = haremManager.haremData.maintenance_activities;

        const appArea = document.getElementById('system-app-area');
        appArea.innerHTML = `
            <div class="maintenance-options">
                <h2>🔧 Maintenance - ${character.name}</h2>

                <div class="current-status">
                    <p><strong>Current Loyalty:</strong> ${character.stats.loyalty}/100</p>
                    <p><strong>Last Interaction:</strong> ${character.last_interaction ?
                        new Date(character.last_interaction).toLocaleDateString() : 'Never'}</p>
                </div>

                <div class="maintenance-activities">
                    <h3>Available Activities</h3>
                    ${Object.entries(activities).map(([activityId, activity]) => `
                        <div class="activity-option">
                            <h4>${activity.name}</h4>
                            <p>${activity.description}</p>
                            <div class="activity-details">
                                <span>Loyalty: +${activity.loyalty_gain}</span>
                                <span>Time: ${activity.time_cost}min</span>
                                ${activity.cp_cost ? `<span>CP: ${activity.cp_cost}</span>` : ''}
                                ${activity.wealth_cost ? `<span>₹${activity.wealth_cost.toLocaleString()}</span>` : ''}
                            </div>
                            <button class="maintenance-btn" onclick="window.systemOS.performMaintenance('${characterId}', '${activityId}')">
                                Perform Activity
                            </button>
                        </div>
                    `).join('')}
                </div>

                <div class="maintenance-actions">
                    <button class="action-btn" onclick="window.systemOS.showMemberDetails('${characterId}')">
                        ← Back to Member
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Perform maintenance activity
     */
    performMaintenance(characterId, activityId) {
        const haremManager = window.haremManager;
        if (!haremManager) return;

        const result = haremManager.performMaintenance(characterId, activityId);

        if (result.success) {
            // Refresh maintenance options
            this.showMaintenanceOptions(characterId);

            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(
                    `${result.message} (+${result.loyalty_gain} loyalty)`
                );
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('maintenance-failed', result.message);
            }
        }
    }

    /**
     * Perform global maintenance
     */
    performGlobalMaintenance() {
        const haremManager = window.haremManager;
        if (!haremManager) return;

        haremManager.processDailyMaintenance();

        // Refresh harem app
        this.openApp('harem');

        if (window.gameErrorHandler) {
            window.gameErrorHandler.showSuccess('Global maintenance completed');
        }
    }
    
    /**
     * Render Contacts app
     */
    renderContactsApp() {
        const characters = this.stateManager.getAllCharacters();
        
        return `
            <div class="contacts-app">
                <h2>👥 Character Profiles</h2>
                <div class="character-grid">
                    ${Object.entries(characters).map(([id, char]) => `
                        <div class="character-card" onclick="window.systemOS.viewCharacterProfile('${id}')">
                            <div class="char-header">
                                <h3>${char.name}</h3>
                                <span class="char-status ${char.conquered ? 'conquered' : 'available'}">${char.status}</span>
                            </div>
                            <div class="char-stats">
                                <div class="stat-bar">
                                    <label>Lust:</label>
                                    <div class="bar"><div class="fill" style="width: ${char.stats.lust}%"></div></div>
                                    <span>${char.stats.lust}</span>
                                </div>
                                <div class="stat-bar">
                                    <label>Corruption:</label>
                                    <div class="bar"><div class="fill" style="width: ${char.stats.corruption}%"></div></div>
                                    <span>${char.stats.corruption}</span>
                                </div>
                                <div class="stat-bar">
                                    <label>Loyalty:</label>
                                    <div class="bar"><div class="fill" style="width: ${char.stats.loyalty}%"></div></div>
                                    <span>${char.stats.loyalty}</span>
                                </div>
                            </div>
                            <div class="char-location">📍 ${char.location}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Render Map app
     */
    renderMapApp() {
        const locations = this.stateManager.state.world.locations;
        const player = this.stateManager.getPlayer();
        const locationManager = window.locationManager;

        return `
            <div class="map-app">
                <h2>🗺️ Navigation</h2>
                <div class="current-location">
                    <strong>Current Location:</strong> ${player.location}
                </div>
                <div class="location-grid">
                    ${Object.entries(locations).map(([locationId, worldData]) => {
                        const locData = locationManager?.locationData?.[locationId];
                        if (!locData) return '';

                        const travelInfo = locationManager?.getTravelTime(player.location, locationId);

                        return `
                            <div class="location-card ${!worldData.unlocked ? 'locked' : ''} ${player.location === locationId ? 'current' : ''}"
                                 ${worldData.unlocked && player.location !== locationId ? `onclick="window.systemOS.travelTo('${locationId}')"` : ''}>
                                <h3>${locData.name}</h3>
                                <p class="location-description">${locData.description}</p>
                                <div class="location-status">
                                    ${worldData.unlocked ? '🔓 Available' : '🔒 Locked'}
                                </div>
                                ${worldData.unlocked && player.location !== locationId && travelInfo ? `
                                    <div class="travel-info">
                                        <small>🕐 ${travelInfo.time}min | 💰 ₹${travelInfo.cost} | ${travelInfo.method}</small>
                                    </div>
                                    <button class="travel-btn">Travel Here</button>
                                ` : ''}
                                ${player.location === locationId ? '<div class="current-marker">📍 You are here</div>' : ''}
                                ${worldData.visit_count ? `<div class="visit-count">Visits: ${worldData.visit_count}</div>` : ''}
                            </div>
                        `;
                    }).join('')}
                </div>

                <div class="map-legend">
                    <h3>Legend</h3>
                    <div class="legend-item">🏠 Residential | 🏫 Educational | 🛍️ Commercial | 🌳 Recreational | 🕉️ Religious</div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render Bank app
     */
    renderBankApp() {
        const player = this.stateManager.getPlayer();
        const financialManager = window.financialManager;
        const summary = financialManager?.getFinancialSummary() || {
            liquid_wealth: player.stats.wealth,
            total_investments: 0,
            total_net_worth: player.stats.wealth,
            weekly_conversions_remaining: 5,
            luxury_items_owned: 0
        };

        return `
            <div class="bank-app">
                <h2>💰 Financial Management</h2>

                <div class="wealth-overview">
                    <div class="wealth-card">
                        <h3>Liquid Wealth</h3>
                        <div class="amount">₹${this.formatCurrency(summary.liquid_wealth)}</div>
                    </div>
                    <div class="wealth-card">
                        <h3>Investments</h3>
                        <div class="amount">₹${this.formatCurrency(summary.total_investments)}</div>
                    </div>
                    <div class="wealth-card">
                        <h3>Net Worth</h3>
                        <div class="amount">₹${this.formatCurrency(summary.total_net_worth)}</div>
                    </div>
                </div>

                <div class="bank-sections">
                    <div class="bank-section">
                        <h3>💎 CP Conversion</h3>
                        <p>Convert ₹1,00,000 to 100 CP</p>
                        <p><strong>Weekly Limit:</strong> ${summary.weekly_conversions_remaining}/5 remaining</p>
                        <button class="bank-btn ${summary.weekly_conversions_remaining > 0 ? '' : 'disabled'}"
                                onclick="window.systemOS.convertWealth()"
                                ${summary.weekly_conversions_remaining > 0 ? '' : 'disabled'}>
                            Convert Wealth to CP
                        </button>
                    </div>

                    <div class="bank-section">
                        <h3>📈 Investments</h3>
                        <div class="investment-options">
                            <div class="investment-item">
                                <h4>Stock Market</h4>
                                <p>12% annual return, medium risk</p>
                                <button class="bank-btn" onclick="window.systemOS.showInvestmentDetails('stocks')">Invest</button>
                            </div>
                            <div class="investment-item">
                                <h4>Real Estate</h4>
                                <p>15% annual return, low risk</p>
                                <button class="bank-btn" onclick="window.systemOS.showInvestmentDetails('real_estate')">Invest</button>
                            </div>
                            <div class="investment-item">
                                <h4>Business Ventures</h4>
                                <p>25% annual return, high risk</p>
                                <button class="bank-btn" onclick="window.systemOS.showInvestmentDetails('business_ventures')">Invest</button>
                            </div>
                        </div>
                    </div>

                    <div class="bank-section">
                        <h3>🎁 Luxury Purchases</h3>
                        <p>Buy luxury items to enhance corruption abilities</p>
                        <div class="luxury-grid">
                            <div class="luxury-item">
                                <h4>Designer Clothes</h4>
                                <p>₹5 Lakh - +10 corruption bonus</p>
                                <button class="bank-btn" onclick="window.systemOS.buyLuxuryItem('designer_clothes')">Buy</button>
                            </div>
                            <div class="luxury-item">
                                <h4>Expensive Jewelry</h4>
                                <p>₹10 Lakh - +15 corruption bonus</p>
                                <button class="bank-btn" onclick="window.systemOS.buyLuxuryItem('jewelry')">Buy</button>
                            </div>
                            <div class="luxury-item">
                                <h4>Luxury Car</h4>
                                <p>₹50 Lakh - +25 corruption bonus</p>
                                <button class="bank-btn" onclick="window.systemOS.buyLuxuryItem('luxury_car')">Buy</button>
                            </div>
                        </div>
                    </div>

                    <div class="bank-section">
                        <h3>💸 Bribes & Influence</h3>
                        <p>Use money to gain influence and remove obstacles</p>
                        <button class="bank-btn disabled">Coming Soon</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render Objectives app
     */
    renderObjectivesApp() {
        const quests = this.stateManager.state.quests;
        const questManager = this.gameEngine.questManager;
        const progressSummary = questManager.getQuestProgressSummary();

        return `
            <div class="objectives-app">
                <h2>📋 Quest Log</h2>

                <div class="quest-summary">
                    <div class="summary-stats">
                        <div class="stat">Main: ${progressSummary.main}</div>
                        <div class="stat">Character: ${progressSummary.character}</div>
                        <div class="stat">Daily: ${progressSummary.daily}</div>
                        <div class="stat">Completed: ${progressSummary.completed}</div>
                    </div>
                </div>

                <div class="quest-tabs">
                    <button class="quest-tab active" onclick="window.systemOS.switchQuestTab('daily')">Daily (${progressSummary.daily})</button>
                    <button class="quest-tab" onclick="window.systemOS.switchQuestTab('main')">Main (${progressSummary.main})</button>
                    <button class="quest-tab" onclick="window.systemOS.switchQuestTab('character')">Character (${progressSummary.character})</button>
                    <button class="quest-tab" onclick="window.systemOS.switchQuestTab('opportunity')">Opportunity (${progressSummary.opportunity})</button>
                </div>

                <div class="quest-content" id="quest-content">
                    ${this.renderQuestTab('daily', quests.daily)}
                </div>
            </div>
        `;
    }
    
    /**
     * Render Shop app
     */
    renderShopApp() {
        return `
            <div class="shop-app">
                <h2>🛒 System Shop</h2>
                <div class="shop-categories">
                    <button class="shop-category active" onclick="window.systemOS.switchShopCategory('consumables')">Consumables</button>
                    <button class="shop-category" onclick="window.systemOS.switchShopCategory('implants')">Implants</button>
                    <button class="shop-category" onclick="window.systemOS.switchShopCategory('software')">Software</button>
                    <button class="shop-category" onclick="window.systemOS.switchShopCategory('pharmaceuticals')">Pharmaceuticals</button>
                </div>
                
                <div class="shop-content" id="shop-content">
                    ${this.renderShopCategory('consumables')}
                </div>
            </div>
        `;
    }
    
    /**
     * Helper methods
     */
    formatCurrency(amount) {
        return (amount / 10000000).toFixed(1) + ' Cr';
    }
    
    getTotalActiveQuests() {
        const quests = this.stateManager.state.quests;
        return quests.main.length + quests.character.length + quests.opportunity.length + quests.daily.length;
    }
    
    renderQuestTab(tabType, quests) {
        if (!quests || quests.length === 0) {
            return `<div class="no-quests">No ${tabType} quests available</div>`;
        }

        return quests.map(quest => {
            const progress = this.calculateQuestProgress(quest);
            const rewards = quest.completion_rewards || quest.rewards || {};

            return `
                <div class="quest-item ${quest.status}">
                    <div class="quest-header">
                        <h4>${quest.title}</h4>
                        <span class="quest-type">${quest.type}</span>
                    </div>

                    <p class="quest-description">${quest.description}</p>

                    ${quest.stages ? `
                        <div class="quest-stages">
                            <div class="stage-progress">Stage ${quest.current_stage + 1}/${quest.stages.length}</div>
                            <div class="current-stage">
                                <strong>${quest.stages[quest.current_stage]?.title || 'Completed'}</strong>
                                <p>${quest.stages[quest.current_stage]?.description || 'All stages completed'}</p>
                            </div>
                        </div>
                    ` : `
                        <div class="quest-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress.percentage}%"></div>
                            </div>
                            <span class="progress-text">${progress.current}/${progress.target}</span>
                        </div>
                    `}

                    <div class="quest-rewards">
                        <strong>Rewards:</strong>
                        ${Object.entries(rewards).map(([stat, value]) =>
                            `<span class="reward-item">${stat.toUpperCase()}: +${value}</span>`
                        ).join(' | ')}
                    </div>

                    ${quest.character ? `
                        <div class="quest-character">
                            <strong>Target:</strong> ${this.stateManager.getCharacter(quest.character)?.name || quest.character}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * Calculate quest progress
     */
    calculateQuestProgress(quest) {
        if (quest.stages) {
            return {
                current: quest.current_stage,
                target: quest.stages.length,
                percentage: Math.round((quest.current_stage / quest.stages.length) * 100)
            };
        } else if (quest.target) {
            const current = quest.progress?.count || quest.progress || 0;
            return {
                current,
                target: quest.target,
                percentage: Math.round((current / quest.target) * 100)
            };
        }

        return { current: 0, target: 1, percentage: 0 };
    }
    
    renderShopCategory(category) {
        const items = {
            consumables: [
                { name: 'Sleeping Pills (Mild)', price: 15, description: 'Causes drowsiness for 2-3 hours' },
                { name: 'Laxative Powder', price: 20, description: 'Odorless powder, effects in 30 minutes' },
                { name: 'Aphrodisiac (Low Grade)', price: 50, description: 'Increases arousal and lowers inhibitions' }
            ]
        };

        const categoryItems = items[category] || [];

        return categoryItems.map(item => `
            <div class="shop-item">
                <h4>${item.name}</h4>
                <p>${item.description}</p>
                <div class="item-price">${item.price} CP</div>
                <button class="buy-btn" onclick="window.systemOS.buyItem('${item.name}', ${item.price})">Buy</button>
            </div>
        `).join('');
    }

    /**
     * Handle character profile viewing
     */
    viewCharacterProfile(characterId) {
        const character = this.stateManager.getCharacter(characterId);
        if (!character) return;

        const appArea = document.getElementById('system-app-area');
        appArea.innerHTML = `
            <div class="character-profile">
                <h2>👤 ${character.name}</h2>
                <div class="profile-details">
                    <div class="profile-section">
                        <h3>Basic Info</h3>
                        <p><strong>Relationship:</strong> ${character.relationship}</p>
                        <p><strong>Age:</strong> ${character.age}</p>
                        <p><strong>Status:</strong> ${character.status}</p>
                        <p><strong>Location:</strong> ${character.location}</p>
                    </div>

                    <div class="profile-section">
                        <h3>Statistics</h3>
                        ${Object.entries(character.stats).map(([stat, value]) => `
                            <div class="stat-bar">
                                <label>${stat.charAt(0).toUpperCase() + stat.slice(1)}:</label>
                                <div class="bar"><div class="fill" style="width: ${value}%"></div></div>
                                <span>${value}</span>
                            </div>
                        `).join('')}
                    </div>

                    <div class="profile-section">
                        <h3>Vulnerabilities</h3>
                        <ul>
                            ${character.vulnerabilities.map(vuln => `<li>${vuln}</li>`).join('')}
                        </ul>
                    </div>

                    ${character.conquered ? `
                        <div class="profile-section conquered-section">
                            <h3>🏆 Conquered</h3>
                            <p>Corruption Path: ${character.corruption_path || 'Unknown'}</p>
                        </div>
                    ` : ''}
                </div>

                <div class="profile-actions">
                    <button class="action-btn" onclick="window.systemOS.openApp('contacts')">← Back to Contacts</button>
                </div>
            </div>
        `;
    }

    /**
     * Handle travel to location
     */
    travelTo(locationId) {
        const locationManager = window.locationManager;
        if (!locationManager) {
            console.error('LocationManager not available');
            return;
        }

        const result = locationManager.travelToLocation(locationId);

        if (result.success) {
            // Close system and show travel result
            this.close();

            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(
                    `${result.message} (${result.time_taken}min, ₹${result.cost})`
                );
            }

            // Update main interface
            if (window.main && window.main.showLocationActions) {
                setTimeout(() => {
                    window.main.showLocationActions();
                }, 500);
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('travel-failed', result.message);
            }
        }
    }

    /**
     * Handle wealth conversion
     */
    convertWealth() {
        const financialManager = window.financialManager;
        if (!financialManager) {
            console.error('FinancialManager not available');
            return;
        }

        const result = financialManager.convertWealthToCP(1);

        if (result.success) {
            // Refresh the bank app
            this.openApp('bank');

            // Show success message
            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(result.message);
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('conversion-failed', result.message);
            }
        }
    }

    /**
     * Show investment details
     */
    showInvestmentDetails(investmentType) {
        const financialManager = window.financialManager;
        if (!financialManager) return;

        const investmentData = financialManager.financialData.investments[investmentType];
        if (!investmentData) return;

        const appArea = document.getElementById('system-app-area');
        appArea.innerHTML = `
            <div class="investment-details">
                <h2>📈 ${investmentData.name}</h2>

                <div class="investment-info">
                    <p><strong>Description:</strong> ${investmentData.description}</p>
                    <p><strong>Return Rate:</strong> ${(investmentData.return_rate * 100).toFixed(1)}% annually</p>
                    <p><strong>Risk Level:</strong> ${investmentData.risk_level}</p>
                    <p><strong>Liquidity:</strong> ${investmentData.liquidity}</p>
                    <p><strong>Minimum Investment:</strong> ₹${investmentData.min_investment.toLocaleString()}</p>
                    <p><strong>Maximum Investment:</strong> ₹${investmentData.max_investment.toLocaleString()}</p>
                </div>

                <div class="investment-form">
                    <h3>Make Investment</h3>
                    <input type="number" id="investment-amount" placeholder="Enter amount"
                           min="${investmentData.min_investment}" max="${investmentData.max_investment}">
                    <button class="bank-btn" onclick="window.systemOS.makeInvestment('${investmentType}')">
                        Invest
                    </button>
                </div>

                <div class="investment-actions">
                    <button class="action-btn" onclick="window.systemOS.openApp('bank')">← Back to Bank</button>
                </div>
            </div>
        `;
    }

    /**
     * Make investment
     */
    makeInvestment(investmentType) {
        const financialManager = window.financialManager;
        if (!financialManager) return;

        const amountInput = document.getElementById('investment-amount');
        const amount = parseInt(amountInput.value);

        if (!amount || amount <= 0) {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('invalid-amount', 'Please enter a valid amount');
            }
            return;
        }

        const result = financialManager.makeInvestment(investmentType, amount);

        if (result.success) {
            // Refresh bank app
            this.openApp('bank');

            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(result.message);
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('investment-failed', result.message);
            }
        }
    }

    /**
     * Buy luxury item
     */
    buyLuxuryItem(itemId) {
        const financialManager = window.financialManager;
        if (!financialManager) return;

        const result = financialManager.purchaseLuxuryItem(itemId);

        if (result.success) {
            // Refresh bank app
            this.openApp('bank');

            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(
                    `${result.message} (+${result.corruption_bonus} corruption bonus)`
                );
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('purchase-failed', result.message);
            }
        }
    }

    /**
     * Switch quest tab
     */
    switchQuestTab(tabType) {
        const quests = this.stateManager.state.quests;
        const questContent = document.getElementById('quest-content');

        // Update tab buttons
        document.querySelectorAll('.quest-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');

        // Update content
        questContent.innerHTML = this.renderQuestTab(tabType, quests[tabType]);
    }

    /**
     * Switch shop category
     */
    switchShopCategory(category) {
        const shopContent = document.getElementById('shop-content');

        // Update category buttons
        document.querySelectorAll('.shop-category').forEach(cat => {
            cat.classList.remove('active');
        });
        event.target.classList.add('active');

        // Update content
        shopContent.innerHTML = this.renderShopCategory(category);
    }

    /**
     * Buy item from shop
     */
    buyItem(itemName, price) {
        const player = this.stateManager.getPlayer();

        if (player.stats.cp >= price) {
            player.stats.cp -= price;
            this.stateManager.addToInventory({ item: itemName, quantity: 1 });

            // Refresh shop display
            this.openApp('shop');

            // Show success message
            if (window.gameErrorHandler) {
                window.gameErrorHandler.showSuccess(`Purchased ${itemName} for ${price} CP!`);
            }
        } else {
            if (window.gameErrorHandler) {
                window.gameErrorHandler.handleError('insufficient-cp', 'Not enough Corruption Points');
            }
        }
    }
}

// Make SystemOS globally accessible
window.SystemOS = SystemOS;
