// New Architecture - Event-Driven Corruption Life Simulator
import { TimeManager } from './core/TimeManager.js';
import { StateManager } from './core/StateManager.js';
import { GameEngine } from './core/GameEngine.js';
import { ScheduleManager } from './core/ScheduleManager.js';
import { SystemOS } from './ui/SystemOS.js';
import { LocationManager } from './data/locations.js';
import { FinancialManager } from './data/financial.js';
import { HaremManager } from './data/harem.js';
import { ConsequenceManager } from './data/consequences.js';
import { initializeUI } from './ui.js';
import { gameErrorHandler } from './errorHandler.js';

// Core system instances
let timeManager;
let stateManager;
let gameEngine;
let scheduleManager;
let systemOS;
let locationManager;
let financialManager;
let haremManager;
let consequenceManager;

/**
 * Initialize all core systems
 */
async function initializeSystems() {
    try {
        console.log('Initializing Corruption Life Simulator...');

        // Initialize core managers
        stateManager = new StateManager();
        timeManager = new TimeManager();
        scheduleManager = new ScheduleManager(stateManager);
        locationManager = new LocationManager(stateManager, timeManager);
        financialManager = new FinancialManager(stateManager, null); // GameEngine will be set later
        haremManager = new HaremManager(stateManager, null); // GameEngine will be set later
        consequenceManager = new ConsequenceManager(stateManager, null); // GameEngine will be set later
        gameEngine = new GameEngine(timeManager, stateManager, scheduleManager);
        systemOS = new SystemOS(stateManager, gameEngine);

        // Set gameEngine reference in managers
        financialManager.gameEngine = gameEngine;
        haremManager.gameEngine = gameEngine;
        consequenceManager.gameEngine = gameEngine;

        // Initialize locations
        locationManager.initializeLocations();

        // Try to load existing save
        const saveLoaded = stateManager.load();
        if (saveLoaded) {
            console.log('Save data loaded successfully');
        } else {
            console.log('Starting new game');
        }

        // Initialize game engine
        gameEngine.initialize();

        // Generate initial schedules
        const currentDay = timeManager.getCurrentDayInfo();
        scheduleManager.generateDailySchedules(currentDay);

        // Make systems globally accessible
        window.timeManager = timeManager;
        window.stateManager = stateManager;
        window.gameEngine = gameEngine;
        window.scheduleManager = scheduleManager;
        window.systemOS = systemOS;
        window.locationManager = locationManager;
        window.financialManager = financialManager;
        window.haremManager = haremManager;
        window.consequenceManager = consequenceManager;

        // Setup UI event handlers
        setupUIEventHandlers();

        // Start the main game loop
        startGameLoop();

        console.log('All systems initialized successfully!');

        // Perform system health check
        performSystemHealthCheck();

    } catch (error) {
        console.error('Failed to initialize systems:', error);
        console.error('Error stack:', error.stack);
        gameErrorHandler.handleError('initialization', error.message);

        // Show detailed error information
        showInitializationError(error);
    }
}

/**
 * Perform system health check
 */
function performSystemHealthCheck() {
    const systems = {
        'TimeManager': window.timeManager,
        'StateManager': window.stateManager,
        'GameEngine': window.gameEngine,
        'ScheduleManager': window.scheduleManager,
        'LocationManager': window.locationManager,
        'FinancialManager': window.financialManager,
        'HaremManager': window.haremManager,
        'ConsequenceManager': window.consequenceManager,
        'SystemOS': window.systemOS
    };

    console.log('🔍 System Health Check:');

    let allHealthy = true;
    Object.entries(systems).forEach(([name, system]) => {
        if (system) {
            console.log(`✅ ${name}: Initialized`);
        } else {
            console.error(`❌ ${name}: Not initialized`);
            allHealthy = false;
        }
    });

    // Check state integrity
    const state = window.stateManager?.state;
    if (state) {
        console.log('✅ Game State: Loaded');
        console.log(`📊 Characters: ${Object.keys(state.characters).length}`);
        console.log(`🏠 Locations: ${Object.keys(state.world.locations).length}`);
        console.log(`💰 Player Wealth: ₹${(state.player.stats.wealth / 10000000).toFixed(1)}Cr`);
        console.log(`⚡ Player CP: ${state.player.stats.cp}`);
    } else {
        console.error('❌ Game State: Not loaded');
        allHealthy = false;
    }

    if (allHealthy) {
        console.log('🎉 All systems healthy!');
        gameErrorHandler.showSuccess('Game systems initialized successfully! 🎮');
    } else {
        console.warn('⚠️ Some systems have issues');
        gameErrorHandler.handleError('system-health', 'Some systems failed to initialize properly');
    }
}

/**
 * Show detailed initialization error
 */
function showInitializationError(error) {
    const storyContainer = document.getElementById('story-container');
    if (storyContainer) {
        storyContainer.innerHTML = `
            <div class="system-error">
                <div class="error-header">
                    <h2>🚨 System Initialization Failed</h2>
                </div>

                <div class="error-details">
                    <h3>Error Details:</h3>
                    <p><strong>Message:</strong> ${error.message}</p>
                    <p><strong>Type:</strong> ${error.name}</p>

                    <h3>Troubleshooting:</h3>
                    <ul>
                        <li>Try refreshing the page (F5)</li>
                        <li>Clear browser cache and reload</li>
                        <li>Check browser console for detailed errors</li>
                        <li>Ensure all game files are properly loaded</li>
                    </ul>

                    <h3>Recovery Options:</h3>
                    <button onclick="location.reload()" class="error-btn">🔄 Reload Game</button>
                    <button onclick="localStorage.clear(); location.reload()" class="error-btn">🗑️ Clear Save & Reload</button>
                </div>

                <div class="error-stack">
                    <details>
                        <summary>Technical Details (for developers)</summary>
                        <pre>${error.stack}</pre>
                    </details>
                </div>
            </div>
        `;
    }
}

/**
 * Setup UI event handlers for the new system
 */
function setupUIEventHandlers() {
    // Character interaction handlers
    gameEngine.on('ui:interaction_result', (data) => {
        showInteractionResult(data);
    });

    // Time update handlers
    gameEngine.on('ui:time_update', (dayInfo) => {
        updateTimeDisplay(dayInfo);
    });

    // Location update handlers
    gameEngine.on('ui:location_update', (data) => {
        updateLocationDisplay(data.location);
    });

    // Event handlers
    gameEngine.on('event:school_encounter', () => {
        showRandomSchoolEvent();
    });

    gameEngine.on('event:night_opportunities', () => {
        showNightOpportunities();
    });
}

/**
 * Start the main game loop
 */
function startGameLoop() {
    // Show the main game interface
    renderMainGameInterface();

    // Start with a welcome message
    showWelcomeMessage();

    // Begin the simulation
    gameErrorHandler.showSuccess('Corruption Life Simulator initialized! 🎮');
}

/**
 * Render the main game interface
 */
function renderMainGameInterface() {
    const storyContainer = document.getElementById('story-container');
    const currentDay = timeManager.getCurrentDayInfo();
    const player = stateManager.getPlayer();

    storyContainer.innerHTML = `
        <div class="game-interface">
            <div class="main-content">
                <div class="current-scene" id="current-scene">
                    <div class="scene-header">
                        <h2>Day ${currentDay.day} - ${currentDay.dayOfWeek}</h2>
                        <p class="time-display">${currentDay.time} - ${currentDay.timeSlot}</p>
                        <p class="location-display">📍 ${player.location}</p>
                    </div>

                    <div class="scene-content" id="scene-content">
                        <!-- Dynamic content will be loaded here -->
                    </div>

                    <div class="action-panel" id="action-panel">
                        <!-- Available actions will be shown here -->
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="quick-stats">
                    <h3>Power Level</h3>
                    <div class="stat-item">CP: <span id="cp-display">${player.stats.cp}</span></div>
                    <div class="stat-item">SP: <span id="sp-display">${player.stats.sp}</span></div>
                    <div class="stat-item">DXP: <span id="dxp-display">${player.stats.dxp}</span></div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn" onclick="window.systemOS.open()">Open System</button>
                    <button class="action-btn" onclick="showLocationActions()">Look Around</button>
                    <button class="action-btn" onclick="showTimeActions()">Manage Time</button>
                </div>

                <div class="character-presence" id="character-presence">
                    <!-- Characters at current location will be shown here -->
                </div>
            </div>
        </div>
    `;

    // Update character presence
    updateCharacterPresence();
}

/**
 * Show welcome message for new simulation
 */
function showWelcomeMessage() {
    const sceneContent = document.getElementById('scene-content');
    const player = stateManager.getPlayer();
    const currentDay = timeManager.getCurrentDayInfo();

    sceneContent.innerHTML = `
        <div class="welcome-content">
            <div class="system-prompt">
                <div class="prompt-title">[CORRUPTION LIFE SIMULATOR ACTIVATED]</div>
                <p>Welcome to your new reality, Vikram. The System has evolved.</p>
                <p>You are no longer just reading a story - you are <strong>living</strong> it.</p>
            </div>

            <p>It's ${currentDay.time} on ${currentDay.dayOfWeek}, Day ${currentDay.day} of your new life.
            You're in your ${player.location}, and the world is waiting for your next move.</p>

            <p>This is a living world. Time passes. People have schedules. Your actions have consequences.
            Every choice you make shapes your path to power.</p>

            <div class="system-prompt">
                <div class="prompt-title">[TUTORIAL: Your New Tools]</div>
                <p>• <strong>[SYSTEM]</strong> button: Access your corruption toolkit</p>
                <p>• <strong>Look Around:</strong> See who's available and what you can do</p>
                <p>• <strong>Manage Time:</strong> Control how time passes</p>
                <p>• <strong>Character interactions:</strong> Build relationships and corruption</p>
            </div>
        `;

    // Show initial actions
    showInitialActions();
}

function isValidChapterNumber(chapterNumber) {
    return Number.isInteger(chapterNumber) && chapterNumber >= 1 && chapterNumber <= 20;
}

async function tryChapterFallbacks(chapterNumber, originalError) {
    console.log('Attempting chapter fallback strategies...');

    // Strategy 1: Try previous chapter if it exists
    if (chapterNumber > 1) {
        try {
            console.log(`Fallback: Loading previous chapter ${chapterNumber - 1}`);
            await loadChapter(chapterNumber - 1);

            // Show user-friendly message about the fallback
            setTimeout(() => {
                gameErrorHandler.showSuccess(`Loaded Chapter ${chapterNumber - 1} instead. Chapter ${chapterNumber} may still be in development! 📝`);
            }, 1000);

            return true;
        } catch (fallbackError) {
            console.log('Previous chapter fallback failed:', fallbackError);
        }
    }

    // Strategy 2: Try chapter 1 as ultimate fallback
    if (chapterNumber !== 1) {
        try {
            console.log('Ultimate fallback: Loading Chapter 1');
            await loadChapter(1);

            setTimeout(() => {
                gameErrorHandler.showSuccess(`Returned to Chapter 1. Chapter ${chapterNumber} is not available yet! 🔄`);
            }, 1000);

            return true;
        } catch (ultimateFallbackError) {
            console.log('Chapter 1 fallback failed:', ultimateFallbackError);
        }
    }

    // Strategy 3: Show emergency content
    showEmergencyContent(chapterNumber, originalError);
    return true;
}

function showEmergencyContent(chapterNumber, error) {
    const storyContainer = document.getElementById('story-container');
    storyContainer.innerHTML = `
        <div class="system-prompt">
            <div class="prompt-title">[EMERGENCY PROTOCOL ACTIVATED]</div>
            <p>🚨 <strong>Chapter ${chapterNumber} is currently unavailable.</strong></p>
            <p>This might happen because:</p>
            <ul>
                <li>The chapter is still being written by Gemini</li>
                <li>There's a temporary loading issue</li>
                <li>The chapter file has an error</li>
            </ul>
            <p><em>Don't worry - your progress is safe!</em></p>
        </div>
        <div class="choice-section">
            <button class="choice-button" onclick="window.main.goToPreviousChapter()">Go to Previous Chapter</button>
            <button class="choice-button" onclick="window.main.startNewGame()">Restart from Chapter 1</button>
            <button class="choice-button" onclick="window.helpSystem.showHelp('troubleshooting')">Get Help</button>
        </div>
    `;
}

// Helper functions for error recovery
function retryCurrentChapter() {
    loadChapter(currentChapterNumber);
}

function goToPreviousChapter() {
    if (previousChapterNumber && previousChapterNumber !== currentChapterNumber) {
        loadChapter(previousChapterNumber);
    } else {
        // If no previous chapter, go to chapter 1
        loadChapter(1);
    }
}

/**
 * Show initial actions available to the player
 */
function showInitialActions() {
    const actionPanel = document.getElementById('action-panel');

    actionPanel.innerHTML = `
        <div class="action-grid">
            <button class="action-btn primary" onclick="window.systemOS.open()">
                🖥️ Open System OS
            </button>
            <button class="action-btn" onclick="showLocationActions()">
                👀 Look Around
            </button>
            <button class="action-btn" onclick="showTimeActions()">
                ⏰ Manage Time
            </button>
            <button class="action-btn" onclick="showCharacterInteractions()">
                👥 Interact
            </button>
        </div>
    `;
}

/**
 * Show actions available at current location
 */
function showLocationActions() {
    const player = stateManager.getPlayer();
    const currentDay = timeManager.getCurrentDayInfo();
    const charactersHere = scheduleManager.getCharactersAtLocation(
        player.location,
        currentDay.dayOfWeek,
        currentDay.timeSlot
    );

    const sceneContent = document.getElementById('scene-content');

    sceneContent.innerHTML = `
        <div class="location-view">
            <h3>Looking around ${player.location}...</h3>

            <div class="location-details">
                <p><strong>Time:</strong> ${currentDay.time} - ${currentDay.timeSlot}</p>
                <p><strong>Day:</strong> ${currentDay.dayOfWeek}, Day ${currentDay.day}</p>
            </div>

            <div class="characters-present">
                <h4>People Here:</h4>
                ${charactersHere.length > 0 ?
                    charactersHere.map(charId => {
                        const char = stateManager.getCharacter(charId);
                        return char ? `
                            <div class="character-present" onclick="showCharacterOptions('${charId}')">
                                <strong>${char.name}</strong> - ${char.relationship}
                                <div class="char-status">${char.status}</div>
                            </div>
                        ` : '';
                    }).join('') :
                    '<p class="no-characters">No one else is here right now.</p>'
                }
            </div>

            <div class="location-actions">
                <h4>Available Actions:</h4>
                <button class="action-btn" onclick="exploreLocation()">🔍 Explore</button>
                <button class="action-btn" onclick="showTravelOptions()">🚶 Travel</button>
                <button class="action-btn" onclick="restAndWait()">💤 Rest & Wait</button>
            </div>
        </div>
    `;
}

/**
 * Show time management options
 */
function showTimeActions() {
    const currentDay = timeManager.getCurrentDayInfo();
    const sceneContent = document.getElementById('scene-content');

    sceneContent.innerHTML = `
        <div class="time-management">
            <h3>Time Management</h3>

            <div class="current-time">
                <p><strong>Current:</strong> ${currentDay.time} - ${currentDay.timeSlot}</p>
                <p><strong>Day:</strong> ${currentDay.dayOfWeek}, Day ${currentDay.day}</p>
            </div>

            <div class="time-actions">
                <h4>Advance Time:</h4>
                <button class="time-btn" onclick="advanceTime('minutes', 30)">+30 Minutes</button>
                <button class="time-btn" onclick="advanceTime('hour', 1)">+1 Hour</button>
                <button class="time-btn" onclick="advanceTime('timeSlot', 1)">Next Time Slot</button>
                <button class="time-btn" onclick="advanceTime('day', 1)">Next Day</button>
            </div>

            <div class="schedule-info">
                <h4>Character Schedules:</h4>
                <button class="action-btn" onclick="showSchedules()">View All Schedules</button>
            </div>
        </div>
    `;
}

/**
 * Advance time by specified amount
 */
function advanceTime(unit, amount) {
    timeManager.advanceTime(unit, amount);

    // Update displays
    updateTimeDisplay(timeManager.getCurrentDayInfo());
    updateCharacterPresence();

    // Show what happened
    const sceneContent = document.getElementById('scene-content');
    sceneContent.innerHTML = `
        <div class="time-advanced">
            <div class="system-prompt">
                <div class="prompt-title">[TIME ADVANCED]</div>
                <p>Time has moved forward by ${amount} ${unit}.</p>
            </div>

            <p>It's now ${timeManager.getCurrentTimeString()} - ${timeManager.getCurrentDayInfo().timeSlot}</p>

            <button class="action-btn" onclick="showLocationActions()">Look Around</button>
        </div>
    `;
}

// Initialize game with save/load functionality
function initializeGame() {
    // Initialize the new systems instead of old UI
    initializeSystems();
}

function showRestoreDialog(saveInfo) {
    const storyContainer = document.getElementById('story-container');
    storyContainer.innerHTML = `
        <div class="system-prompt">
            <div class="prompt-title">[SAVE DATA DETECTED]</div>
            <p>🎮 <strong>Previous game session found!</strong></p>
            <p><strong>Chapter:</strong> ${saveInfo.chapter}</p>
            <p><strong>Last played:</strong> ${saveInfo.timeAgo}</p>
            <p><strong>Stats:</strong> CP: ${saveInfo.playerStats.cp || 0}, SP: ${saveInfo.playerStats.sp || 0}, DXP: ${saveInfo.playerStats.dxp || 0}</p>
        </div>
        <div class="choice-section">
            <button class="choice-button" onclick="window.main.continueGame()">Continue Previous Game</button>
            <button class="choice-button" onclick="window.main.startNewGame()">Start New Game</button>
        </div>
    `;
}

function continueGame() {
    const savedState = gameState.load();
    if (savedState && gameState.restore(savedState)) {
        loadChapter(savedState.currentChapter);
        gameErrorHandler.showSuccess('Game restored! Welcome back! 🎮');
    } else {
        gameErrorHandler.handleError('loading', 'Failed to restore save data');
        startNewGame();
    }
}

function startNewGame() {
    // Reset to default state
    gameState.resetToSafeState();
    loadChapter(1);
}

/**
 * Show character interaction options
 */
function showCharacterInteractions() {
    const player = stateManager.getPlayer();
    const currentDay = timeManager.getCurrentDayInfo();
    const charactersHere = scheduleManager.getCharactersAtLocation(
        player.location,
        currentDay.dayOfWeek,
        currentDay.timeSlot
    );

    const sceneContent = document.getElementById('scene-content');

    if (charactersHere.length === 0) {
        sceneContent.innerHTML = `
            <div class="no-interactions">
                <p>There's no one here to interact with right now.</p>
                <p>Try looking around or traveling to find people.</p>
                <button class="action-btn" onclick="showLocationActions()">Look Around</button>
            </div>
        `;
        return;
    }

    sceneContent.innerHTML = `
        <div class="character-interactions">
            <h3>Character Interactions</h3>

            <div class="available-characters">
                ${charactersHere.map(charId => {
                    const char = stateManager.getCharacter(charId);
                    if (!char) return '';

                    return `
                        <div class="interaction-character">
                            <div class="char-info">
                                <h4>${char.name}</h4>
                                <p>${char.relationship} - ${char.status}</p>
                                <div class="char-stats-mini">
                                    <span>Lust: ${char.stats.lust}</span>
                                    <span>Corruption: ${char.stats.corruption}</span>
                                    <span>Loyalty: ${char.stats.loyalty}</span>
                                </div>
                            </div>

                            <div class="interaction-options">
                                <button class="interact-btn" onclick="interactWithCharacter('${charId}', 'observe')">👁️ Observe</button>
                                <button class="interact-btn" onclick="interactWithCharacter('${charId}', 'talk')">💬 Talk</button>
                                <button class="interact-btn" onclick="interactWithCharacter('${charId}', 'seduce')">😏 Seduce</button>
                                <button class="interact-btn" onclick="interactWithCharacter('${charId}', 'intimidate')">😠 Intimidate</button>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

/**
 * Interact with a character
 */
function interactWithCharacter(characterId, action) {
    // Emit interaction event to game engine
    gameEngine.emit('player:interact', {
        characterId,
        action,
        options: {}
    });
}

/**
 * Show interaction result
 */
function showInteractionResult(data) {
    const { characterId, action, result } = data;
    const character = stateManager.getCharacter(characterId);

    const sceneContent = document.getElementById('scene-content');
    sceneContent.innerHTML = `
        <div class="interaction-result">
            <div class="system-prompt">
                <div class="prompt-title">[INTERACTION: ${action.toUpperCase()}]</div>
                <p><strong>Target:</strong> ${character.name}</p>
                <p><strong>Result:</strong> ${result.success ? 'Success' : 'Failed'}</p>
            </div>

            <div class="result-description">
                <p>${result.message}</p>
            </div>

            ${result.effects && Object.keys(result.effects).length > 0 ? `
                <div class="stat-changes">
                    <h4>Effects:</h4>
                    ${Object.entries(result.effects).map(([stat, change]) =>
                        `<p>${stat}: ${change > 0 ? '+' : ''}${change}</p>`
                    ).join('')}
                </div>
            ` : ''}

            <div class="continue-actions">
                <button class="action-btn" onclick="showCharacterInteractions()">Continue Interacting</button>
                <button class="action-btn" onclick="showLocationActions()">Look Around</button>
            </div>
        </div>
    `;
}

/**
 * Update time display
 */
function updateTimeDisplay(dayInfo) {
    const timeElements = document.querySelectorAll('.time-display');
    timeElements.forEach(el => {
        el.textContent = `${dayInfo.time} - ${dayInfo.timeSlot}`;
    });

    // Update HUD if it exists
    const hudTime = document.getElementById('hud-time');
    if (hudTime) {
        hudTime.textContent = dayInfo.time;
    }
}

/**
 * Update location display
 */
function updateLocationDisplay(location) {
    const locationElements = document.querySelectorAll('.location-display');
    locationElements.forEach(el => {
        el.textContent = `📍 ${location}`;
    });

    // Update HUD if it exists
    const hudLocation = document.getElementById('hud-location');
    if (hudLocation) {
        hudLocation.textContent = location;
    }
}

/**
 * Update character presence display
 */
function updateCharacterPresence() {
    const presenceElement = document.getElementById('character-presence');
    if (!presenceElement) return;

    const player = stateManager.getPlayer();
    const currentDay = timeManager.getCurrentDayInfo();
    const charactersHere = scheduleManager.getCharactersAtLocation(
        player.location,
        currentDay.dayOfWeek,
        currentDay.timeSlot
    );

    presenceElement.innerHTML = `
        <h4>People Here:</h4>
        ${charactersHere.length > 0 ?
            charactersHere.map(charId => {
                const char = stateManager.getCharacter(charId);
                return char ? `<div class="char-present">${char.name}</div>` : '';
            }).join('') :
            '<div class="no-chars">No one here</div>'
        }
    `;
}

// Placeholder functions for actions referenced in UI
function exploreLocation() {
    gameErrorHandler.showSuccess('Exploration feature coming soon! 🔍');
}

function showTravelOptions() {
    gameErrorHandler.showSuccess('Travel system coming soon! 🚶');
}

function restAndWait() {
    advanceTime('minutes', 30);
}

function showSchedules() {
    gameErrorHandler.showSuccess('Schedule viewer coming soon! 📅');
}

function showRandomSchoolEvent() {
    gameErrorHandler.showSuccess('Random school event triggered! 🏫');
}

function showNightOpportunities() {
    gameErrorHandler.showSuccess('Night opportunities available! 🌙');
}

/**
 * Test all systems functionality
 */
function testAllSystems() {
    console.log('🧪 Running system tests...');

    const tests = [
        () => testTimeManager(),
        () => testStateManager(),
        () => testCharacterSystem(),
        () => testLocationSystem(),
        () => testFinancialSystem(),
        () => testQuestSystem(),
        () => testHaremSystem()
    ];

    let passed = 0;
    let failed = 0;

    tests.forEach((test, index) => {
        try {
            test();
            console.log(`✅ Test ${index + 1} passed`);
            passed++;
        } catch (error) {
            console.error(`❌ Test ${index + 1} failed:`, error);
            failed++;
        }
    });

    console.log(`🧪 Test Results: ${passed} passed, ${failed} failed`);
    return failed === 0;
}

function testTimeManager() {
    const currentTime = window.timeManager.getCurrentDayInfo();
    if (!currentTime.day || !currentTime.dayOfWeek || !currentTime.timeSlot) {
        throw new Error('TimeManager not working properly');
    }
}

function testStateManager() {
    const player = window.stateManager.getPlayer();
    if (!player || typeof player.stats.wealth !== 'number') {
        throw new Error('StateManager not working properly');
    }
}

function testCharacterSystem() {
    const characters = window.stateManager.getAllCharacters();
    if (!characters || Object.keys(characters).length === 0) {
        throw new Error('Character system not working properly');
    }
}

function testLocationSystem() {
    const locations = window.stateManager.state.world.locations;
    if (!locations || Object.keys(locations).length === 0) {
        throw new Error('Location system not working properly');
    }
}

function testFinancialSystem() {
    const summary = window.financialManager.getFinancialSummary();
    if (!summary || typeof summary.liquid_wealth !== 'number') {
        throw new Error('Financial system not working properly');
    }
}

function testQuestSystem() {
    const quests = window.stateManager.state.quests;
    if (!quests || !Array.isArray(quests.daily)) {
        throw new Error('Quest system not working properly');
    }
}

function testHaremSystem() {
    const summary = window.haremManager.getHaremSummary();
    if (!summary || typeof summary.total_members !== 'number') {
        throw new Error('Harem system not working properly');
    }
}

// Update global exports for new system
window.main = {
    showLocationActions,
    showTimeActions,
    showCharacterInteractions,
    interactWithCharacter,
    advanceTime,
    exploreLocation,
    showTravelOptions,
    restAndWait,
    showSchedules,
    testAllSystems,
    performSystemHealthCheck
};

// Initial game start
initializeGame();