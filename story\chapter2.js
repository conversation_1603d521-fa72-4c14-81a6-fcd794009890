export const chapterData = {
    "start": {
        "onLoad": function() {
            window.player.location = "Home";
            window.player.time = "Afternoon";
        },
        "text": `
            <p>Two weeks pass. The System is quiet, but you are not. Using your [Mental Resonance Scan], you listen to your family's anxieties, their petty jealousies, their desperation. You see only weakness.</p>
            <p>The day of your final hospital check-up arrives, and you are officially discharged. That afternoon, a man in an impeccably tailored suit arrives. Advocate <PERSON><PERSON>, your father's personal lawyer.</p>
            <p>"Mrs. <PERSON>, ladies, if you would excuse us," he says politely but firmly. "My client's instructions were explicit. This conversation is for <PERSON><PERSON><PERSON>'s ears alone."</p>
            <p>Alone in the living room, he opens his briefcase.</p>
        `,
        "choices": [
            { "label": "Listen to the lawyer.", "leadsTo": "revelation" }
        ]
    },
    "revelation": {
        "text": `
            <p>"V<PERSON><PERSON>," the lawyer begins, "Your father was not who he seemed. He was a discreet and exceptionally successful investor. His will is simple. You, his only son, are the sole beneficiary of his entire private estate."</p>
            <p>He slides a thick file across the table. You see numbers that make your head spin. Offshore accounts. Property deeds. A stock portfolio.</p>
            <p>"What... what is the total value?" you ask, your voice a whisper.</p>
            <p>Advocate Mehra looks you straight in the eye. "As of this morning's valuation, just over <strong>two hundred and twelve crore rupees.</strong>"</p>
            <div class="system-prompt">
                <div class="prompt-title">[World Event Triggered: The Inheritance]</div>
                <span class="reward">[Massive Shift in Personal Status Detected: Pauper -> Crorepati]</span><br>
                <span class="reward">[New Feature Unlocked: [Wealth Conversion]]</span><br>
                <span class="reward">[Title Unlocked: The Hidden King]</span>
            </div>
            <p>The grief for your father is replaced by a chilling clarity. You see the path forward.</p>
        `,
        "choices": [
            { "label": "Make your decision.", "leadsTo": "decision" }
        ]
    },
    "decision": {
        "onLoad": function() {
            window.player.skills.push("Deception (Lvl 2)");
        },
        "text": `
            <p>"Mr. Mehra," you say, your voice now firm, devoid of any youthful tremor. "No one is to know about this. Not my mother, not my sisters. My father's 'official' life is the only one they will ever know. Is that understood?"</p>
            <p>The lawyer smiles faintly. "Your secret is safe, Mr. Singh."</p>
            <p>After he leaves, you lie to your family, telling them it was just about pension formalities. The lie comes as easily as breathing. You are the Hidden King. You will let them suffer, let them squirm, and make them dance for scraps from a fortune they can't imagine.</p>
            <p>That night, a quest you were too scared to accept before now seems like the only logical step.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Chain Quest Initiated: The Queen's Conquest (Stage 1)]</div>
                <strong>Description:</strong> A king's first conquest is his own domain. To control the kingdom, you must first own its heart, body, and soul.<br>
                <strong>Objective:</strong> Breach the first wall of her sanctity. Administer one dose of [Sleeping Pills (Mild)] into her nightly glass of milk.<br>
                <span class="reward"><strong>Reward:</strong> +200 CP, +200 DXP, Unlocks Quest Stage 2.</span>
            </div>
        `,
        "choices": [
            { "label": "Accept the Quest. Her fall begins tonight.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.quests.push("The Queen's Conquest");
        },
        "text": `
            <p>You accept. Your heart hammers against your ribs, a primal drumbeat of fear and <strong>exhilarating, monstrous excitement</strong>. You are not her son anymore. You are a force of nature, and she is in your path.</p>
            <hr>
        `,
        "choices": [
            { "label": "Proceed to Chapter 3", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 3 }
        ]
    }
};
export const modals = {};