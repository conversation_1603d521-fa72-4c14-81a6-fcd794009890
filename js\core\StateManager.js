/**
 * StateManager - Centralized state management for the Corruption Life Simulator
 * Handles all game state including player, characters, world state, and persistence
 */

import { CharacterManager, characterData } from '../data/characters.js';

export class StateManager {
    constructor() {
        this.saveKey = 'predator_ascent_v2_save';
        this.autoSaveInterval = 30000; // Auto-save every 30 seconds
        this.autoSaveTimer = null;
        this.lastSaveTime = 0;

        // Initialize character manager
        this.characterManager = new CharacterManager(this);

        // Initialize state structure
        this.initializeState();

        // Initialize characters with detailed data
        this.characterManager.initializeCharacters();

        this.setupAutoSave();
        this.setupBeforeUnload();
    }
    
    /**
     * Initialize default game state
     */
    initializeState() {
        this.state = {
            // Player data
            player: {
                stats: {
                    cp: 0,      // Corruption Points
                    sp: 0,      // System Points
                    dxp: 0,     // Dominance Experience
                    wealth: 20000000000, // 200 Crore inheritance
                    reputation: 0,
                    influence: 0
                },
                skills: [],
                inventory: [],
                location: 'Home',

                // New player attributes
                traits: [],
                achievements: [],
                secrets: [],
                blackmail_material: []
            },

            // Character relationship data - will be populated by CharacterManager
            characters: {},
            
            // World state
            world: {
                locations: {
                    'Home': { unlocked: true, discovered_secrets: [] },
                    'School': { unlocked: true, discovered_secrets: [] },
                    'Mall': { unlocked: false, discovered_secrets: [] },
                    'Park': { unlocked: false, discovered_secrets: [] },
                    'Temple': { unlocked: false, discovered_secrets: [] }
                },
                events: [],
                rumors: [],
                consequences: []
            },
            
            // Quest system
            quests: {
                main: [],
                character: [],
                opportunity: [],
                daily: [],
                completed: []
            },
            
            // Harem management
            harem: {
                members: [],
                loyalty_decay_rate: 1, // Points per day
                last_maintenance: null
            },
            
            // System shop
            shop: {
                unlocked_categories: ['Consumables'],
                purchase_history: [],
                available_items: {}
            },
            
            // Game meta
            meta: {
                version: '2.0',
                created: Date.now(),
                last_played: Date.now(),
                playtime: 0,
                choices_made: 0,
                endings_unlocked: []
            }
        };
    }
    
    /**
     * Get player data
     */
    getPlayer() {
        return this.state.player;
    }
    
    /**
     * Get character data
     */
    getCharacter(characterId) {
        return this.state.characters[characterId];
    }
    
    /**
     * Get all characters
     */
    getAllCharacters() {
        return this.state.characters;
    }
    
    /**
     * Update character stats
     */
    updateCharacterStats(characterId, statUpdates) {
        const character = this.state.characters[characterId];
        if (character) {
            Object.assign(character.stats, statUpdates);
            character.last_interaction = Date.now();

            // Update character mood based on new stats
            character.current_mood = this.characterManager.getCharacterMood(characterId);

            // Check for corruption milestones
            this.checkCorruptionMilestones(characterId);

            this.triggerAutoSave();
        }
    }

    /**
     * Check for corruption milestones and trigger events
     */
    checkCorruptionMilestones(characterId) {
        const character = this.state.characters[characterId];
        if (!character) return;

        const { corruption, lust, loyalty } = character.stats;

        // Corruption milestones
        if (corruption >= 25 && !character.milestones?.corruption_25) {
            this.triggerMilestone(characterId, 'corruption_25', 'First corruption milestone reached');
        }
        if (corruption >= 50 && !character.milestones?.corruption_50) {
            this.triggerMilestone(characterId, 'corruption_50', 'Halfway corrupted');
        }
        if (corruption >= 75 && !character.milestones?.corruption_75) {
            this.triggerMilestone(characterId, 'corruption_75', 'Nearly completely corrupted');
        }
        if (corruption >= 100 && !character.milestones?.corruption_100) {
            this.triggerMilestone(characterId, 'corruption_100', 'Complete corruption achieved');
            character.conquered = true;
        }

        // Lust milestones
        if (lust >= 50 && !character.milestones?.lust_50) {
            this.triggerMilestone(characterId, 'lust_50', 'High arousal state');
        }

        // Loyalty milestones
        if (loyalty <= 10 && !character.milestones?.loyalty_low) {
            this.triggerMilestone(characterId, 'loyalty_low', 'Rebellion risk detected');
        }
    }

    /**
     * Trigger character milestone
     */
    triggerMilestone(characterId, milestoneId, description) {
        const character = this.state.characters[characterId];
        if (!character.milestones) character.milestones = {};

        character.milestones[milestoneId] = {
            achieved: Date.now(),
            description
        };

        // Emit milestone event
        if (window.gameEngine) {
            window.gameEngine.emit('character:milestone', {
                characterId,
                milestoneId,
                description
            });
        }
    }

    /**
     * Start corruption path for character
     */
    startCorruptionPath(characterId, pathId) {
        const character = this.state.characters[characterId];
        if (!character) return false;

        const charData = this.characterManager.characterData?.[characterId];
        if (!charData || !charData.corruption_paths?.[pathId]) return false;

        character.corruption_path = pathId;
        character.corruption_progress = {
            path: pathId,
            stage: 0,
            started: Date.now(),
            completed_stages: []
        };

        this.triggerAutoSave();
        return true;
    }

    /**
     * Advance corruption path stage
     */
    advanceCorruptionStage(characterId) {
        const character = this.state.characters[characterId];
        if (!character || !character.corruption_progress) return false;

        const progress = character.corruption_progress;
        progress.stage++;
        progress.completed_stages.push({
            stage: progress.stage - 1,
            completed: Date.now()
        });

        this.triggerAutoSave();
        return true;
    }

    /**
     * Get character's corruption path info
     */
    getCorruptionPathInfo(characterId) {
        const character = this.state.characters[characterId];
        if (!character || !character.corruption_path) return null;

        const charData = this.characterManager.characterData?.[characterId];
        if (!charData) return null;

        const pathData = charData.corruption_paths[character.corruption_path];
        const progress = character.corruption_progress;

        return {
            ...pathData,
            current_stage: progress?.stage || 0,
            total_stages: pathData.stages?.length || 0,
            progress_percent: Math.round(((progress?.stage || 0) / (pathData.stages?.length || 1)) * 100)
        };
    }
    
    /**
     * Add to player stats
     */
    addPlayerStats(statUpdates) {
        Object.keys(statUpdates).forEach(stat => {
            if (this.state.player.stats[stat] !== undefined) {
                this.state.player.stats[stat] += statUpdates[stat];
            }
        });
        this.triggerAutoSave();
    }
    
    /**
     * Add item to inventory
     */
    addToInventory(item) {
        this.state.player.inventory.push({
            ...item,
            acquired: Date.now()
        });
        this.triggerAutoSave();
    }
    
    /**
     * Remove item from inventory
     */
    removeFromInventory(itemName, quantity = 1) {
        const inventory = this.state.player.inventory;
        for (let i = inventory.length - 1; i >= 0; i--) {
            if (inventory[i].item === itemName) {
                if (inventory[i].quantity > quantity) {
                    inventory[i].quantity -= quantity;
                    break;
                } else {
                    inventory.splice(i, 1);
                    quantity -= inventory[i].quantity || 1;
                    if (quantity <= 0) break;
                }
            }
        }
        this.triggerAutoSave();
    }
    
    /**
     * Add quest
     */
    addQuest(questType, questData) {
        if (this.state.quests[questType]) {
            this.state.quests[questType].push({
                ...questData,
                id: this.generateId(),
                created: Date.now(),
                status: 'active'
            });
            this.triggerAutoSave();
        }
    }
    
    /**
     * Complete quest
     */
    completeQuest(questId) {
        Object.keys(this.state.quests).forEach(questType => {
            const questIndex = this.state.quests[questType].findIndex(q => q.id === questId);
            if (questIndex !== -1) {
                const quest = this.state.quests[questType][questIndex];
                quest.status = 'completed';
                quest.completed = Date.now();
                this.state.quests.completed.push(quest);
                this.state.quests[questType].splice(questIndex, 1);
            }
        });
        this.triggerAutoSave();
    }
    
    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    /**
     * Setup auto-save
     */
    setupAutoSave() {
        this.autoSaveTimer = setInterval(() => {
            this.autoSave();
        }, this.autoSaveInterval);
    }
    
    /**
     * Setup before unload save
     */
    setupBeforeUnload() {
        window.addEventListener('beforeunload', () => {
            this.save();
        });
        
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.save();
            }
        });
    }
    
    /**
     * Trigger auto-save
     */
    triggerAutoSave() {
        const timeSinceLastSave = Date.now() - this.lastSaveTime;
        if (timeSinceLastSave > 5000) { // At least 5 seconds since last save
            this.save();
        }
    }
    
    /**
     * Auto-save
     */
    autoSave() {
        const timeSinceLastSave = Date.now() - this.lastSaveTime;
        if (timeSinceLastSave > 25000) { // At least 25 seconds since last save
            this.save();
        }
    }
    
    /**
     * Save game state with all systems
     */
    save() {
        try {
            // Update meta information
            this.state.meta.last_played = Date.now();
            this.state.meta.version = '2.0';

            // Collect state from all managers
            const completeState = {
                ...this.state,

                // Time manager state
                time_state: window.timeManager?.saveState() || null,

                // Schedule manager state
                schedule_state: window.scheduleManager?.saveState() || null,

                // Financial manager state
                financial_state: this.getPlayer().financial || null,

                // Harem manager state
                harem_state: this.state.harem || null,

                // Consequence manager state
                consequence_state: {
                    reputation: this.state.world?.reputation || {},
                    crisis_levels: this.state.world?.crisis_levels || {},
                    pending_consequences: this.state.world?.pending_consequences || []
                }
            };

            // Create backup of previous save
            const existingSave = localStorage.getItem(this.saveKey);
            if (existingSave) {
                localStorage.setItem(this.saveKey + '_backup', existingSave);
            }

            // Save new state
            localStorage.setItem(this.saveKey, JSON.stringify(completeState));
            this.lastSaveTime = Date.now();

            console.log('Game saved successfully with all systems');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);

            // Try to restore backup if save failed
            try {
                const backup = localStorage.getItem(this.saveKey + '_backup');
                if (backup) {
                    localStorage.setItem(this.saveKey, backup);
                    console.log('Restored backup save after save failure');
                }
            } catch (backupError) {
                console.error('Failed to restore backup:', backupError);
            }

            return false;
        }
    }
    
    /**
     * Load game state and restore all systems
     */
    load() {
        try {
            const savedData = localStorage.getItem(this.saveKey);
            if (!savedData) {
                console.log('No save data found');
                return false;
            }

            const loadedState = JSON.parse(savedData);
            if (!this.validateSaveData(loadedState)) {
                console.error('Save data validation failed');
                return false;
            }

            // Load main state
            this.state = {
                player: loadedState.player || this.state.player,
                characters: loadedState.characters || this.state.characters,
                world: loadedState.world || this.state.world,
                quests: loadedState.quests || this.state.quests,
                harem: loadedState.harem || loadedState.harem_state || this.state.harem,
                shop: loadedState.shop || this.state.shop,
                meta: loadedState.meta || this.state.meta
            };

            // Restore system states after a delay to ensure managers are initialized
            setTimeout(() => {
                this.restoreSystemStates(loadedState);
            }, 100);

            console.log('Game loaded successfully');
            return true;
        } catch (error) {
            console.error('Failed to load save data:', error);

            // Try to load backup
            try {
                const backupData = localStorage.getItem(this.saveKey + '_backup');
                if (backupData) {
                    console.log('Attempting to load backup save...');
                    const backupState = JSON.parse(backupData);
                    if (this.validateSaveData(backupState)) {
                        this.state = backupState;
                        console.log('Backup save loaded successfully');
                        return true;
                    }
                }
            } catch (backupError) {
                console.error('Failed to load backup save:', backupError);
            }

            return false;
        }
    }

    /**
     * Restore states to all system managers
     */
    restoreSystemStates(loadedState) {
        try {
            // Restore time manager state
            if (loadedState.time_state && window.timeManager) {
                window.timeManager.loadState(loadedState.time_state);
                console.log('Time manager state restored');
            }

            // Restore schedule manager state
            if (loadedState.schedule_state && window.scheduleManager) {
                window.scheduleManager.loadState(loadedState.schedule_state);
                console.log('Schedule manager state restored');
            }

            // Financial state is already in player.financial
            if (loadedState.financial_state && this.state.player) {
                this.state.player.financial = loadedState.financial_state;
                console.log('Financial state restored');
            }

            // Harem state is already in this.state.harem
            if (loadedState.harem_state) {
                this.state.harem = loadedState.harem_state;
                console.log('Harem state restored');
            }

            // Restore consequence state
            if (loadedState.consequence_state && this.state.world) {
                this.state.world.reputation = loadedState.consequence_state.reputation || {};
                this.state.world.crisis_levels = loadedState.consequence_state.crisis_levels || {};
                this.state.world.pending_consequences = loadedState.consequence_state.pending_consequences || [];
                console.log('Consequence state restored');
            }

            // Re-initialize character manager with loaded data
            if (this.characterManager) {
                this.characterManager.initializeCharacters();
                console.log('Character manager re-initialized');
            }

            // Trigger save to ensure consistency
            this.triggerAutoSave();

        } catch (error) {
            console.error('Error restoring system states:', error);
        }
    }
    
    /**
     * Validate save data
     */
    validateSaveData(saveData) {
        return saveData && 
               saveData.player && 
               saveData.characters && 
               saveData.meta && 
               saveData.meta.version;
    }
    
    /**
     * Get save info
     */
    getSaveInfo() {
        try {
            const savedData = localStorage.getItem(this.saveKey);
            if (!savedData) return null;
            
            const saveData = JSON.parse(savedData);
            return {
                version: saveData.meta?.version,
                lastPlayed: saveData.meta?.last_played,
                playtime: saveData.meta?.playtime,
                playerLevel: saveData.player?.stats?.cp || 0
            };
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Delete save
     */
    deleteSave() {
        localStorage.removeItem(this.saveKey);
        localStorage.removeItem(this.saveKey + '_backup');
    }
    
    /**
     * Reset to default state
     */
    reset() {
        this.initializeState();
        this.save();
    }
    
    /**
     * Destroy manager
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
    }
}
