/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    background-attachment: fixed;
    color: #e8e8e8;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.1em;
    font-weight: 400;
    line-height: 1.8;
    margin: 0;
    padding: 40px 20px 140px 20px;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(25, 25, 25, 0.8);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* --- HUD --- */
.hud {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 15, 0.98) 100%);
    border-top: 3px solid #d4af37;
    padding: 16px 24px;
    box-sizing: border-box;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 0.95em;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    z-index: 1000;
    backdrop-filter: blur(20px);
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.5);
}

.hud-item {
    margin: 8px 20px;
    color: #f4d03f;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    transition: all 0.3s ease;
}

.hud-item:hover {
    color: #fff;
    transform: translateY(-1px);
}

.hud-item strong {
    color: #b8b8b8;
    font-weight: 600;
    margin-right: 8px;
}

.system-button {
    cursor: pointer;
    border: 1px solid #d4af37;
    padding: 5px 10px;
    border-radius: 5px;
}

.system-button:hover {
    background-color: rgba(212, 175, 55, 0.1);
}

/* --- Story & Prompts --- */
.system-prompt {
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.6) 0%, rgba(30, 50, 90, 0.4) 100%);
    border: 2px solid rgba(102, 170, 255, 0.6);
    border-radius: 12px;
    padding: 24px;
    margin: 32px 0;
    position: relative;
    box-shadow: 0 8px 32px rgba(102, 170, 255, 0.1);
    backdrop-filter: blur(5px);
}

.system-prompt::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #66aaff, #4488dd, #66aaff);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.3;
}

.prompt-title {
    color: #88ccff;
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.reward {
    color: #4ade80;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.penalty {
    color: #f87171;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* --- Choices --- */
.choice-section {
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(40, 40, 40, 0.6) 100%);
    border-left: 6px solid #d4af37;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.choice-button {
    display: block;
    width: 100%;
    margin: 20px 0;
    padding: 20px 24px;
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    text-align: center;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.05em;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.choice-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.choice-button:hover {
    background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4);
}

.choice-button:hover::before {
    left: 100%;
}

.choice-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

/* --- Special Text --- */
p {
    margin: 1.2em 0;
    text-align: justify;
}

em {
    color: #c0c0c0;
    font-style: italic;
    font-weight: 300;
}

strong {
    color: #ff7b7b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.character-name {
    color: #d4af37;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.character-name:hover {
    color: #f4d03f;
    background: rgba(212, 175, 55, 0.1);
    border-bottom-color: #d4af37;
    text-shadow: 0 1px 4px rgba(212, 175, 55, 0.5);
}

/* --- Modals --- */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
    margin: 8% auto;
    padding: 32px;
    border: 2px solid #d4af37;
    width: 90%;
    max-width: 700px;
    border-radius: 16px;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    position: relative;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
    color: #d4af37;
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 24px;
    border-bottom: 2px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.close-button {
    color: #888;
    position: absolute;
    top: 20px;
    right: 24px;
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 50%;
}

.close-button:hover,
.close-button:focus {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.modal-body ul {
    list-style: none;
    padding: 0;
}

.modal-body li {
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #d4af37;
}

.system-menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.system-menu-button {
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.system-menu-button:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: #d4af37;
    transform: translateY(-5px);
    color: #f4d03f;
}

.system-menu-button.disabled {
    color: #666;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.2);
}
.system-menu-button.disabled:hover {
    transform: none;
    border-color: rgba(255, 255, 255, 0.1);
}

@keyframes fadeIn {
    from { opacity: 0; } to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; } to { opacity: 0; }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-30px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Scene content animations */
.scene-content {
    animation: slideInContent 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInContent {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* --- Error Handling Styles --- */
.game-error-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.game-error-container.visible {
    opacity: 1;
    pointer-events: all;
}

.game-error-container.hidden {
    display: none;
}

.error-modal {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #ff6b6b;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(255, 107, 107, 0.3);
    animation: errorSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 14px 14px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.error-title {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 600;
    font-size: 1.2em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    line-height: 1;
}

.error-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.error-content {
    padding: 32px;
}

.error-message {
    color: #e8e8e8;
    line-height: 1.6;
    margin-bottom: 24px;
}

.error-message ul {
    margin: 16px 0;
    padding-left: 20px;
}

.error-message li {
    margin: 8px 0;
    color: #c0c0c0;
}

.error-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.error-action-button {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.error-action-button:hover {
    background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(212, 175, 55, 0.4);
}

.success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: 0 8px 24px rgba(74, 222, 128, 0.3);
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-notification.visible {
    opacity: 1;
    transform: translateX(0);
}

.report-dialog h3 {
    color: #d4af37;
    margin-bottom: 16px;
}

.report-dialog button {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 16px;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* --- Help System Styles --- */
.help-system-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.help-system-container.visible {
    opacity: 1;
    pointer-events: all;
}

.help-system-container.hidden {
    display: none;
}

.help-modal {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #d4af37;
    border-radius: 16px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
    animation: helpSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.help-header {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    padding: 20px 24px;
    border-radius: 14px 14px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.help-title {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 600;
    font-size: 1.2em;
}

.help-close {
    background: none;
    border: none;
    color: #000;
    font-size: 24px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    line-height: 1;
}

.help-close:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

.help-content {
    padding: 0;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

.help-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.help-tab {
    flex: 1;
    background: none;
    border: none;
    color: #c0c0c0;
    padding: 16px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.help-tab:hover {
    background: rgba(212, 175, 55, 0.1);
    color: #f4d03f;
}

.help-tab.active {
    color: #d4af37;
    border-bottom-color: #d4af37;
    background: rgba(212, 175, 55, 0.1);
}

.help-tab-content {
    padding: 32px;
    color: #e8e8e8;
    line-height: 1.6;
}

.help-section {
    margin-bottom: 32px;
}

.help-section h3 {
    color: #d4af37;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.help-section h4 {
    color: #f4d03f;
    margin-bottom: 12px;
    margin-top: 20px;
}

.help-section ul {
    margin: 12px 0;
    padding-left: 20px;
}

.help-section li {
    margin: 8px 0;
    color: #c0c0c0;
}

.help-section code {
    background: rgba(212, 175, 55, 0.1);
    color: #f4d03f;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9em;
}

.help-section kbd {
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.85em;
    border: 1px solid #666;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Quick Tips */
.quick-tip {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 500;
    box-shadow: 0 8px 24px rgba(74, 222, 128, 0.3);
    z-index: 10001;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 500px;
    text-align: center;
}

.quick-tip.visible {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

@keyframes helpSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* --- Emergency Tools Styles --- */
.emergency-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(15px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.emergency-panel.visible {
    opacity: 1;
    pointer-events: all;
}

.emergency-modal {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 3px solid #ff6b6b;
    border-radius: 16px;
    max-width: 700px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(255, 107, 107, 0.5);
    animation: emergencySlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.emergency-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 13px 13px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.emergency-title {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 600;
    font-size: 1.2em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.emergency-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    line-height: 1;
}

.emergency-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.emergency-content {
    padding: 32px;
    color: #e8e8e8;
    line-height: 1.6;
}

.emergency-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 24px 0;
}

.emergency-tool {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 16px 20px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    text-align: center;
    font-size: 0.95em;
}

.emergency-tool:hover {
    background: linear-gradient(135deg, #ff8a8a 0%, #ff6b6b 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

.emergency-tool:active {
    transform: translateY(0);
}

.emergency-info {
    margin-top: 32px;
    padding: 20px;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 12px;
    border-left: 4px solid #ff6b6b;
}

.emergency-info h4 {
    color: #ff6b6b;
    margin-bottom: 8px;
    margin-top: 16px;
}

.emergency-info h4:first-child {
    margin-top: 0;
}

.emergency-info p {
    margin: 8px 0;
    color: #c0c0c0;
}

@keyframes emergencySlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 20px 16px 160px 16px; font-size: 1em;
    }
    .container {
        padding: 24px; border-radius: 12px;
    }
    .hud {
        padding: 12px 16px; font-size: 0.85em;
    }
    .hud-item {
        margin: 6px 12px;
    }
    .choice-button {
        padding: 16px 20px; font-size: 1em;
    }
    .modal-content {
        margin: 5% auto; padding: 24px; width: 95%;
    }
    .system-prompt {
        padding: 20px; margin: 24px 0;
    }

    .error-modal {
        width: 95%;
        margin: 20px;
    }

    .error-content {
        padding: 24px;
    }

    .error-actions {
        flex-direction: column;
    }

    .error-action-button {
        width: 100%;
    }

    .help-modal {
        width: 95%;
        margin: 10px;
    }

    .help-tabs {
        flex-wrap: wrap;
    }

    .help-tab {
        flex: 1 1 50%;
        min-width: 120px;
    }

    .help-tab-content {
        padding: 20px;
    }

    .emergency-modal {
        width: 95%;
        margin: 10px;
    }

    .emergency-content {
        padding: 20px;
    }

    .emergency-tools-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .quick-tip {
        max-width: 90%;
        padding: 12px 16px;
        font-size: 0.9em;
    }

    /* Mobile System OS */
    .game-interface {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        border-left: none;
        border-top: 1px solid #333;
    }

    .system-desktop {
        width: 95%;
        height: 95%;
    }

    .system-content {
        flex-direction: column;
    }

    .system-dock {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
    }
}

/* System OS Styles */
.system-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.system-desktop {
    width: 90%;
    height: 90%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #d4af37;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.system-header {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
}

.system-close {
    cursor: pointer;
    font-size: 1.5em;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background 0.3s;
}

.system-close:hover {
    background: rgba(0, 0, 0, 0.2);
}

.system-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.system-dock {
    width: 200px;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.system-app-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    color: #fff;
}

.system-app-icon:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-2px);
}

.system-app-icon.active {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
}

.app-icon {
    font-size: 2em;
    margin-bottom: 5px;
}

.app-name {
    font-size: 0.9em;
    font-weight: 600;
    text-align: center;
}

.system-app-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    color: #fff;
}

.system-status {
    background: rgba(0, 0, 0, 0.5);
    padding: 10px 20px;
    display: flex;
    gap: 30px;
    border-top: 1px solid #333;
}

.status-item {
    color: #d4af37;
    font-weight: 600;
}

/* Game Interface Styles */
.game-interface {
    display: flex;
    height: 100vh;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.sidebar {
    width: 300px;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-left: 1px solid #333;
}

.current-scene {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.scene-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.scene-header h2 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

.time-display, .location-display {
    color: #888;
    margin: 5px 0;
}

.scene-content {
    min-height: 300px;
    margin-bottom: 20px;
    line-height: 1.6;
}

.action-panel {
    border-top: 1px solid #333;
    padding-top: 20px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: #fff;
    border: 1px solid #666;
    padding: 15px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    text-align: center;
}

.action-btn:hover {
    background: linear-gradient(135deg, #555 0%, #777 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.action-btn.primary {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #e6c547 0%, #c49a00 100%);
}

.quick-stats {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.quick-stats h3 {
    color: #d4af37;
    margin: 0 0 15px 0;
    text-align: center;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    color: #fff;
}

.quick-actions {
    margin-bottom: 20px;
}

.quick-actions .action-btn {
    width: 100%;
    margin-bottom: 10px;
}

.character-presence {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
}

.character-presence h4 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

.char-present {
    background: rgba(212, 175, 55, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    margin: 5px 0;
    border-left: 3px solid #d4af37;
}

.no-chars {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 10px;
}

/* Character interaction styles */
.character-present {
    background: rgba(212, 175, 55, 0.1);
    padding: 12px;
    border-radius: 8px;
    margin: 8px 0;
    border-left: 3px solid #d4af37;
    cursor: pointer;
    transition: all 0.3s;
}

.character-present:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateX(5px);
}

.char-status {
    font-size: 0.9em;
    color: #888;
    margin-top: 5px;
}

.interaction-character {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    margin: 15px 0;
    border: 1px solid #333;
}

.char-info h4 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

.char-stats-mini {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    font-size: 0.9em;
}

.char-stats-mini span {
    color: #888;
}

.interaction-options {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.interact-btn {
    background: linear-gradient(135deg, #444 0%, #666 100%);
    color: #fff;
    border: 1px solid #777;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s;
}

.interact-btn:hover {
    background: linear-gradient(135deg, #666 0%, #888 100%);
    transform: translateY(-1px);
}

/* Time management styles */
.time-btn {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: #fff;
    border: 1px solid #3b82f6;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    margin: 5px;
    transition: all 0.3s;
}

.time-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    transform: translateY(-1px);
}

/* Welcome screen styles */
.welcome-content {
    text-align: center;
    padding: 20px;
}

.welcome-screen {
    max-width: 800px;
    margin: 0 auto;
}

.system-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
}

.stat-card h3 {
    color: #d4af37;
    margin: 0 0 15px 0;
    text-align: center;
}

.stat-card p {
    margin: 8px 0;
    color: #ccc;
}

/* System OS App Styles */
.contacts-app, .map-app, .bank-app, .objectives-app, .shop-app {
    height: 100%;
}

.character-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.character-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
    cursor: pointer;
    transition: all 0.3s;
}

.character-card:hover {
    border-color: #d4af37;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.char-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.char-header h3 {
    color: #d4af37;
    margin: 0;
}

.char-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
}

.char-status.conquered {
    background: #dc2626;
    color: #fff;
}

.char-status.available {
    background: #16a34a;
    color: #fff;
}

.stat-bar {
    display: flex;
    align-items: center;
    margin: 8px 0;
    gap: 10px;
}

.stat-bar label {
    width: 80px;
    font-size: 0.9em;
    color: #888;
}

.bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: linear-gradient(90deg, #d4af37 0%, #b38f00 100%);
    transition: width 0.3s;
}

.char-location {
    margin-top: 10px;
    font-size: 0.9em;
    color: #888;
}

/* Enhanced System OS Styles */
.quest-summary {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-stats {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.summary-stats .stat {
    background: rgba(212, 175, 55, 0.1);
    padding: 8px 15px;
    border-radius: 6px;
    border: 1px solid #d4af37;
    color: #d4af37;
    font-weight: 600;
}

.quest-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    margin: 15px 0;
    border: 1px solid #333;
    transition: all 0.3s;
}

.quest-item:hover {
    border-color: #d4af37;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.quest-item.completed {
    opacity: 0.7;
    border-color: #4a9;
}

.quest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.quest-type {
    background: #d4af37;
    color: #000;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
}

.quest-description {
    color: #ccc;
    margin: 10px 0;
    line-height: 1.4;
}

.quest-stages {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.stage-progress {
    color: #d4af37;
    font-weight: 600;
    margin-bottom: 10px;
}

.current-stage {
    color: #fff;
}

.current-stage strong {
    color: #d4af37;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #d4af37 0%, #b38f00 100%);
    transition: width 0.3s;
}

.progress-text {
    color: #888;
    font-size: 0.9em;
}

.quest-rewards {
    margin: 15px 0;
    padding: 10px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 6px;
    border-left: 3px solid #d4af37;
}

.reward-item {
    color: #d4af37;
    font-weight: 600;
    margin: 0 5px;
}

.quest-character {
    margin: 10px 0;
    color: #888;
}

/* Bank App Enhancements */
.wealth-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.wealth-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
    text-align: center;
}

.wealth-card h3 {
    color: #d4af37;
    margin: 0 0 10px 0;
    font-size: 1em;
}

.wealth-card .amount {
    font-size: 1.5em;
    font-weight: bold;
    color: #fff;
}

.bank-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.bank-section {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
}

.bank-section h3 {
    color: #d4af37;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bank-btn {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    margin: 5px;
}

.bank-btn:hover {
    background: linear-gradient(135deg, #e6c547 0%, #c49a00 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.bank-btn.disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.investment-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.investment-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #444;
}

.investment-item h4 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

.luxury-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.luxury-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #444;
    text-align: center;
}

.luxury-item h4 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

/* Harem App Styles */
.harem-overview {
    margin-bottom: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.overview-stats .stat-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
    text-align: center;
}

.overview-stats .stat-card h3 {
    color: #d4af37;
    margin: 0 0 10px 0;
    font-size: 1em;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #fff;
}

.stat-value.warning {
    color: #ff6b6b;
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.harem-member {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
    transition: all 0.3s;
}

.harem-member:hover {
    border-color: #d4af37;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.harem-member.at-risk {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.member-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.member-header h4 {
    color: #d4af37;
    margin: 0;
}

.member-status {
    font-size: 1.2em;
}

.member-stats {
    margin: 15px 0;
}

.member-info {
    margin: 15px 0;
    color: #ccc;
}

.member-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.harem-btn {
    background: linear-gradient(135deg, #666 0%, #888 100%);
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    flex: 1;
}

.harem-btn:hover {
    background: linear-gradient(135deg, #888 0%, #aaa 100%);
    transform: translateY(-1px);
}

.harem-btn.maintenance {
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
}

.harem-btn.maintenance:hover {
    background: linear-gradient(135deg, #e6c547 0%, #c49a00 100%);
}

/* Map App Enhancements */
.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.location-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #333;
    cursor: pointer;
    transition: all 0.3s;
}

.location-card:hover {
    border-color: #d4af37;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.location-card.locked {
    opacity: 0.5;
    cursor: not-allowed;
}

.location-card.current {
    border-color: #4a9;
    background: rgba(68, 170, 153, 0.1);
}

.location-description {
    color: #ccc;
    font-size: 0.9em;
    margin: 10px 0;
    line-height: 1.4;
}

.travel-info {
    margin: 10px 0;
    color: #888;
    font-size: 0.8em;
}

.travel-btn {
    background: linear-gradient(135deg, #4a9 0%, #369 100%);
    color: #fff;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    width: 100%;
    margin-top: 10px;
    transition: all 0.3s;
}

.travel-btn:hover {
    background: linear-gradient(135deg, #5ba 0%, #47a 100%);
    transform: translateY(-1px);
}

.current-marker {
    background: #4a9;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
    text-align: center;
    margin-top: 10px;
}

.visit-count {
    color: #888;
    font-size: 0.8em;
    margin-top: 5px;
}

.map-legend {
    margin-top: 30px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid #333;
}

.map-legend h3 {
    color: #d4af37;
    margin: 0 0 10px 0;
}

.legend-item {
    color: #ccc;
    font-size: 0.9em;
}

/* Error Display Styles */
.system-error {
    max-width: 800px;
    margin: 50px auto;
    padding: 30px;
    background: rgba(255, 0, 0, 0.1);
    border: 2px solid #ff4444;
    border-radius: 12px;
    color: #fff;
}

.error-header {
    text-align: center;
    margin-bottom: 30px;
}

.error-header h2 {
    color: #ff4444;
    margin: 0;
}

.error-details {
    margin-bottom: 30px;
}

.error-details h3 {
    color: #ff6666;
    margin: 20px 0 10px 0;
}

.error-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

.error-details li {
    margin: 5px 0;
    color: #ccc;
}

.error-btn {
    background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    margin: 10px 10px 10px 0;
    transition: all 0.3s;
}

.error-btn:hover {
    background: linear-gradient(135deg, #ff6666 0%, #ff0000 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
}

.error-stack {
    margin-top: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid #333;
}

.error-stack details {
    color: #ccc;
}

.error-stack summary {
    color: #d4af37;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 10px;
}

.error-stack pre {
    background: rgba(0, 0, 0, 0.5);
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-size: 0.8em;
    color: #ff9999;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    border-top-color: #d4af37;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-message {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #d4af37;
    font-weight: 600;
    margin: 20px 0;
}

/* Success Messages */
.success-message {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid #4a9;
    color: #4a9;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.success-message::before {
    content: "✅";
    font-size: 1.2em;
}

/* Warning Messages */
.warning-message {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid #ffa500;
    color: #ffa500;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.warning-message::before {
    content: "⚠️";
    font-size: 1.2em;
}

/* Info Messages */
.info-message {
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid #0096ff;
    color: #0096ff;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-message::before {
    content: "ℹ️";
    font-size: 1.2em;
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .game-interface {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .sidebar {
        width: 100%;
        border-left: none;
        border-top: 1px solid #333;
        order: 2;
    }

    .main-content {
        order: 1;
        padding: 15px;
    }

    .system-desktop {
        width: 95%;
        height: 95%;
    }

    .system-content {
        flex-direction: column;
    }

    .system-dock {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
        padding: 10px;
    }

    .system-app-icon {
        min-width: 80px;
        padding: 10px 5px;
    }

    .wealth-overview {
        grid-template-columns: 1fr;
    }

    .members-grid {
        grid-template-columns: 1fr;
    }

    .location-grid {
        grid-template-columns: 1fr;
    }

    .investment-options {
        grid-template-columns: 1fr;
    }

    .luxury-grid {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: 1fr;
    }

    .summary-stats {
        flex-direction: column;
        gap: 10px;
    }

    .overview-stats {
        grid-template-columns: 1fr;
    }

    .system-error {
        margin: 20px;
        padding: 20px;
    }

    .error-btn {
        width: 100%;
        margin: 5px 0;
    }
}