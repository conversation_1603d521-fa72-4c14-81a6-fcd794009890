/**
 * Harem Management System - Manages conquered characters and their abilities
 */

export const haremData = {
    // Loyalty decay rates based on character type
    loyalty_decay: {
        'family': 0.5,    // Family members decay slower
        'student': 1.0,   // Students decay at normal rate
        'teacher': 0.8,   // Teachers decay slower
        'stranger': 1.5   // Strangers decay faster
    },
    
    // Maintenance activities to restore loyalty
    maintenance_activities: {
        'quality_time': {
            name: 'Quality Time',
            description: 'Spend intimate time together',
            loyalty_gain: 10,
            time_cost: 60, // minutes
            cp_cost: 0,
            requirements: []
        },
        
        'expensive_gift': {
            name: 'Expensive Gift',
            description: 'Buy an expensive gift to show appreciation',
            loyalty_gain: 15,
            time_cost: 30,
            cp_cost: 0,
            wealth_cost: 50000,
            requirements: []
        },
        
        'sexual_session': {
            name: 'Sexual Session',
            description: 'Intense sexual encounter to reinforce dominance',
            loyalty_gain: 20,
            time_cost: 90,
            cp_cost: 10,
            requirements: ['high_corruption']
        },
        
        'public_display': {
            name: 'Public Display',
            description: 'Make them perform publicly to show ownership',
            loyalty_gain: 25,
            time_cost: 120,
            cp_cost: 20,
            requirements: ['complete_submission']
        },
        
        'punishment': {
            name: 'Punishment',
            description: 'Discipline for disobedience',
            loyalty_gain: 30,
            time_cost: 60,
            cp_cost: 15,
            requirements: ['fear_based_control'],
            risk: 'high' // Can backfire if done wrong
        }
    },
    
    // Special abilities unlocked by conquered characters
    character_abilities: {
        'sonia': {
            'home_access': {
                name: 'Home Access',
                description: 'Sonia gives you complete access to the house',
                effects: ['unlock_all_home_areas', 'no_suspicion_at_home'],
                unlock_condition: 'loyalty >= 80'
            },
            'family_influence': {
                name: 'Family Influence',
                description: 'Sonia helps corrupt other family members',
                effects: ['family_corruption_bonus', 'reduced_family_suspicion'],
                unlock_condition: 'loyalty >= 90 && corruption >= 80'
            },
            'financial_control': {
                name: 'Financial Control',
                description: 'Access to family finances and property',
                effects: ['monthly_allowance', 'property_access'],
                unlock_condition: 'loyalty >= 95 && corruption >= 90'
            }
        },
        
        'natasha': {
            'social_network': {
                name: 'Social Network',
                description: 'Access to Natasha\'s social circle',
                effects: ['new_targets', 'social_events'],
                unlock_condition: 'loyalty >= 70'
            },
            'workplace_access': {
                name: 'Workplace Access',
                description: 'Access to her workplace and colleagues',
                effects: ['office_location', 'colleague_targets'],
                unlock_condition: 'loyalty >= 80 && corruption >= 60'
            },
            'sugar_baby_network': {
                name: 'Sugar Baby Network',
                description: 'Natasha recruits other sugar babies for you',
                effects: ['passive_income', 'new_targets'],
                unlock_condition: 'loyalty >= 90 && corruption >= 80'
            }
        },
        
        'tanya': {
            'gym_access': {
                name: 'Gym Access',
                description: 'Access to gym and fitness targets',
                effects: ['gym_location', 'fitness_targets'],
                unlock_condition: 'loyalty >= 70'
            },
            'fighting_skills': {
                name: 'Fighting Skills',
                description: 'Tanya can intimidate targets for you',
                effects: ['intimidation_bonus', 'protection'],
                unlock_condition: 'loyalty >= 80 && corruption >= 70'
            },
            'underground_network': {
                name: 'Underground Network',
                description: 'Access to underground fighting and crime',
                effects: ['illegal_activities', 'criminal_contacts'],
                unlock_condition: 'loyalty >= 90 && corruption >= 85'
            }
        },
        
        'kavya': {
            'student_network': {
                name: 'Student Network',
                description: 'Access to school social circles',
                effects: ['student_targets', 'school_events'],
                unlock_condition: 'loyalty >= 60'
            },
            'academic_help': {
                name: 'Academic Help',
                description: 'Kavya helps with school work and reputation',
                effects: ['academic_bonus', 'teacher_approval'],
                unlock_condition: 'loyalty >= 75 && corruption >= 50'
            },
            'recruitment_tool': {
                name: 'Recruitment Tool',
                description: 'Kavya helps recruit other students',
                effects: ['student_recruitment', 'group_activities'],
                unlock_condition: 'loyalty >= 85 && corruption >= 75'
            }
        }
    },
    
    // Rebellion risks and consequences
    rebellion_system: {
        risk_factors: {
            'low_loyalty': { threshold: 20, risk_multiplier: 2.0 },
            'high_suspicion': { threshold: 70, risk_multiplier: 1.5 },
            'neglect': { days_without_interaction: 7, risk_multiplier: 1.8 },
            'public_humiliation': { recent_events: true, risk_multiplier: 2.5 },
            'family_discovery': { family_knows: true, risk_multiplier: 3.0 }
        },
        
        rebellion_consequences: {
            'minor_rebellion': {
                effects: ['loyalty_loss', 'temporary_unavailability'],
                duration: '2 days',
                recovery_difficulty: 'easy'
            },
            'major_rebellion': {
                effects: ['significant_loyalty_loss', 'suspicion_increase', 'family_alert'],
                duration: '1 week',
                recovery_difficulty: 'hard'
            },
            'complete_rebellion': {
                effects: ['character_lost', 'police_investigation', 'family_hostility'],
                duration: 'permanent',
                recovery_difficulty: 'impossible'
            }
        }
    }
};

/**
 * Harem Manager - Manages conquered characters and their abilities
 */
export class HaremManager {
    constructor(stateManager, gameEngine) {
        this.stateManager = stateManager;
        this.gameEngine = gameEngine;
        this.haremData = haremData;

        // Initialize harem state
        this.initializeHaremState();

        // Setup event listeners only if gameEngine is available
        if (this.gameEngine) {
            this.setupEventListeners();
        }
    }
    
    /**
     * Initialize harem state
     */
    initializeHaremState() {
        const harem = this.stateManager.state.harem;
        
        if (!harem.maintenance_log) {
            harem.maintenance_log = [];
        }
        
        if (!harem.abilities_unlocked) {
            harem.abilities_unlocked = {};
        }
        
        if (!harem.rebellion_warnings) {
            harem.rebellion_warnings = {};
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.gameEngine) {
            console.warn('HaremManager: Cannot setup event listeners - gameEngine not available');
            return;
        }

        // Listen for character conquest
        this.gameEngine.on('character:conquest', (data) => {
            this.addToHarem(data.characterId);
        });

        // Listen for daily maintenance
        this.gameEngine.on('world:new_day', () => {
            this.processDailyMaintenance();
        });

        // Listen for loyalty changes
        this.gameEngine.on('character:loyalty', (data) => {
            this.checkRebellionRisk(data.characterId);
        });
    }

    /**
     * Initialize event listeners after gameEngine is set
     */
    initializeEventListeners() {
        this.setupEventListeners();
    }
    
    /**
     * Add character to harem
     */
    addToHarem(characterId) {
        const harem = this.stateManager.state.harem;
        const character = this.stateManager.getCharacter(characterId);
        
        if (!character || harem.members.includes(characterId)) {
            return false;
        }
        
        // Add to harem
        harem.members.push(characterId);
        character.conquered = true;
        character.conquest_date = Date.now();
        
        // Initialize character-specific harem data
        if (!harem.abilities_unlocked[characterId]) {
            harem.abilities_unlocked[characterId] = [];
        }
        
        if (!harem.rebellion_warnings[characterId]) {
            harem.rebellion_warnings[characterId] = {
                level: 0,
                last_warning: null
            };
        }
        
        // Check for immediate ability unlocks
        this.checkAbilityUnlocks(characterId);
        
        // Emit harem addition event
        this.gameEngine.emit('harem:member_added', { characterId, character });
        
        this.stateManager.triggerAutoSave();
        return true;
    }
    
    /**
     * Remove character from harem (rebellion/escape)
     */
    removeFromHarem(characterId, reason = 'rebellion') {
        const harem = this.stateManager.state.harem;
        const character = this.stateManager.getCharacter(characterId);
        
        if (!character || !harem.members.includes(characterId)) {
            return false;
        }
        
        // Remove from harem
        const index = harem.members.indexOf(characterId);
        harem.members.splice(index, 1);
        
        character.conquered = false;
        character.rebellion_date = Date.now();
        character.rebellion_reason = reason;
        
        // Reset stats based on rebellion severity
        if (reason === 'complete_rebellion') {
            character.stats.loyalty = 0;
            character.stats.fear = 100;
            character.stats.suspicion = 100;
        }
        
        // Remove abilities
        delete harem.abilities_unlocked[characterId];
        delete harem.rebellion_warnings[characterId];
        
        // Emit harem removal event
        this.gameEngine.emit('harem:member_lost', { characterId, character, reason });
        
        this.stateManager.triggerAutoSave();
        return true;
    }
    
    /**
     * Process daily loyalty decay and maintenance
     */
    processDailyMaintenance() {
        const harem = this.stateManager.state.harem;
        const decayRate = harem.loyalty_decay_rate || 1;
        
        harem.members.forEach(characterId => {
            const character = this.stateManager.getCharacter(characterId);
            if (!character) return;
            
            // Calculate decay based on character type
            const characterType = this.getCharacterType(characterId);
            const typeDecay = this.haremData.loyalty_decay[characterType] || 1.0;
            const totalDecay = decayRate * typeDecay;
            
            // Apply loyalty decay
            const oldLoyalty = character.stats.loyalty;
            character.stats.loyalty = Math.max(0, character.stats.loyalty - totalDecay);
            
            // Log maintenance need
            if (character.stats.loyalty < 50) {
                this.logMaintenanceNeed(characterId, 'low_loyalty');
            }
            
            // Check for rebellion risk
            this.checkRebellionRisk(characterId);
            
            // Update last maintenance check
            harem.last_maintenance = Date.now();
        });
        
        this.stateManager.triggerAutoSave();
    }
    
    /**
     * Perform maintenance activity on character
     */
    performMaintenance(characterId, activityId) {
        const character = this.stateManager.getCharacter(characterId);
        const activity = this.haremData.maintenance_activities[activityId];
        const player = this.stateManager.getPlayer();
        
        if (!character || !activity) {
            return { success: false, message: 'Invalid character or activity' };
        }
        
        // Check requirements
        if (!this.checkMaintenanceRequirements(characterId, activity)) {
            return { success: false, message: 'Requirements not met' };
        }
        
        // Check costs
        if (activity.cp_cost && player.stats.cp < activity.cp_cost) {
            return { success: false, message: 'Insufficient CP' };
        }
        
        if (activity.wealth_cost && player.stats.wealth < activity.wealth_cost) {
            return { success: false, message: 'Insufficient wealth' };
        }
        
        // Perform activity
        if (activity.cp_cost) player.stats.cp -= activity.cp_cost;
        if (activity.wealth_cost) player.stats.wealth -= activity.wealth_cost;
        
        // Apply loyalty gain
        const loyaltyGain = this.calculateLoyaltyGain(characterId, activity);
        character.stats.loyalty = Math.min(100, character.stats.loyalty + loyaltyGain);
        
        // Advance time
        this.gameEngine.timeManager.advanceTime('minutes', activity.time_cost);
        
        // Log maintenance
        this.logMaintenance(characterId, activityId, loyaltyGain);
        
        // Check for ability unlocks
        this.checkAbilityUnlocks(characterId);
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Performed ${activity.name} with ${character.name}`,
            loyalty_gain: loyaltyGain,
            time_cost: activity.time_cost
        };
    }
    
    /**
     * Check maintenance requirements
     */
    checkMaintenanceRequirements(characterId, activity) {
        const character = this.stateManager.getCharacter(characterId);
        
        return activity.requirements.every(requirement => {
            switch (requirement) {
                case 'high_corruption':
                    return character.stats.corruption >= 60;
                case 'complete_submission':
                    return character.stats.corruption >= 80 && character.stats.loyalty >= 80;
                case 'fear_based_control':
                    return character.stats.fear >= 50;
                default:
                    return true;
            }
        });
    }
    
    /**
     * Calculate loyalty gain from maintenance
     */
    calculateLoyaltyGain(characterId, activity) {
        const character = this.stateManager.getCharacter(characterId);
        let gain = activity.loyalty_gain;
        
        // Bonus based on character's current state
        if (character.stats.lust > 60) gain *= 1.2;
        if (character.stats.corruption > 80) gain *= 1.1;
        
        // Penalty for high suspicion
        if (character.stats.suspicion > 50) gain *= 0.8;
        
        return Math.round(gain);
    }
    
    /**
     * Check for ability unlocks
     */
    checkAbilityUnlocks(characterId) {
        const character = this.stateManager.getCharacter(characterId);
        const abilities = this.haremData.character_abilities[characterId];
        const harem = this.stateManager.state.harem;
        
        if (!abilities) return;
        
        Object.keys(abilities).forEach(abilityId => {
            const ability = abilities[abilityId];
            
            // Skip if already unlocked
            if (harem.abilities_unlocked[characterId]?.includes(abilityId)) {
                return;
            }
            
            // Check unlock condition
            if (this.evaluateUnlockCondition(character, ability.unlock_condition)) {
                // Unlock ability
                if (!harem.abilities_unlocked[characterId]) {
                    harem.abilities_unlocked[characterId] = [];
                }
                harem.abilities_unlocked[characterId].push(abilityId);
                
                // Apply ability effects
                this.applyAbilityEffects(characterId, abilityId, ability);
                
                // Emit unlock event
                this.gameEngine.emit('harem:ability_unlocked', {
                    characterId,
                    abilityId,
                    ability
                });
            }
        });
    }
    
    /**
     * Evaluate unlock condition
     */
    evaluateUnlockCondition(character, condition) {
        // Simple condition parser
        // Format: "loyalty >= 80 && corruption >= 60"
        const stats = character.stats;
        
        // Replace stat names with actual values
        let evalCondition = condition
            .replace(/loyalty/g, stats.loyalty)
            .replace(/corruption/g, stats.corruption)
            .replace(/lust/g, stats.lust)
            .replace(/fear/g, stats.fear)
            .replace(/love/g, stats.love)
            .replace(/suspicion/g, stats.suspicion);
        
        try {
            return eval(evalCondition);
        } catch (error) {
            console.error('Error evaluating unlock condition:', error);
            return false;
        }
    }
    
    /**
     * Apply ability effects
     */
    applyAbilityEffects(characterId, abilityId, ability) {
        const worldState = this.stateManager.state.world;
        
        ability.effects.forEach(effect => {
            switch (effect) {
                case 'unlock_all_home_areas':
                    // Unlock all home areas
                    break;
                case 'monthly_allowance':
                    // Setup monthly allowance
                    break;
                case 'new_targets':
                    // Add new character targets
                    break;
                // Add more effect handlers as needed
            }
        });
    }
    
    /**
     * Check rebellion risk
     */
    checkRebellionRisk(characterId) {
        const character = this.stateManager.getCharacter(characterId);
        const harem = this.stateManager.state.harem;
        
        if (!character || !harem.members.includes(characterId)) return;
        
        let riskLevel = 0;
        const riskFactors = this.haremData.rebellion_system.risk_factors;
        
        // Calculate risk based on factors
        if (character.stats.loyalty <= riskFactors.low_loyalty.threshold) {
            riskLevel += riskFactors.low_loyalty.risk_multiplier;
        }
        
        if (character.stats.suspicion >= riskFactors.high_suspicion.threshold) {
            riskLevel += riskFactors.high_suspicion.risk_multiplier;
        }
        
        // Check for neglect
        const daysSinceInteraction = character.last_interaction ? 
            (Date.now() - character.last_interaction) / (24 * 60 * 60 * 1000) : 999;
        
        if (daysSinceInteraction >= riskFactors.neglect.days_without_interaction) {
            riskLevel += riskFactors.neglect.risk_multiplier;
        }
        
        // Update rebellion warning level
        const warnings = harem.rebellion_warnings[characterId];
        if (riskLevel > 3) {
            warnings.level = Math.min(3, warnings.level + 1);
            warnings.last_warning = Date.now();
            
            // Emit rebellion warning
            this.gameEngine.emit('harem:rebellion_warning', {
                characterId,
                level: warnings.level,
                riskLevel
            });
            
            // Trigger rebellion if risk is too high
            if (riskLevel > 5 && Math.random() < 0.3) {
                this.triggerRebellion(characterId, riskLevel);
            }
        } else if (riskLevel < 1) {
            warnings.level = Math.max(0, warnings.level - 1);
        }
    }
    
    /**
     * Trigger character rebellion
     */
    triggerRebellion(characterId, riskLevel) {
        const character = this.stateManager.getCharacter(characterId);
        let rebellionType;
        
        if (riskLevel > 8) {
            rebellionType = 'complete_rebellion';
        } else if (riskLevel > 5) {
            rebellionType = 'major_rebellion';
        } else {
            rebellionType = 'minor_rebellion';
        }
        
        const consequences = this.haremData.rebellion_system.rebellion_consequences[rebellionType];
        
        // Apply consequences
        consequences.effects.forEach(effect => {
            switch (effect) {
                case 'character_lost':
                    this.removeFromHarem(characterId, 'complete_rebellion');
                    break;
                case 'loyalty_loss':
                    character.stats.loyalty = Math.max(0, character.stats.loyalty - 20);
                    break;
                case 'significant_loyalty_loss':
                    character.stats.loyalty = Math.max(0, character.stats.loyalty - 40);
                    break;
                case 'suspicion_increase':
                    character.stats.suspicion = Math.min(100, character.stats.suspicion + 30);
                    break;
                // Add more consequence handlers
            }
        });
        
        // Emit rebellion event
        this.gameEngine.emit('harem:rebellion', {
            characterId,
            type: rebellionType,
            consequences
        });
    }
    
    /**
     * Get character type for decay calculation
     */
    getCharacterType(characterId) {
        const familyMembers = ['sonia', 'natasha', 'tanya'];
        const students = ['kavya'];
        
        if (familyMembers.includes(characterId)) return 'family';
        if (students.includes(characterId)) return 'student';
        return 'stranger';
    }
    
    /**
     * Log maintenance activity
     */
    logMaintenance(characterId, activityId, loyaltyGain) {
        const harem = this.stateManager.state.harem;
        
        harem.maintenance_log.push({
            characterId,
            activityId,
            loyaltyGain,
            timestamp: Date.now()
        });
        
        // Keep only last 50 entries
        if (harem.maintenance_log.length > 50) {
            harem.maintenance_log = harem.maintenance_log.slice(-50);
        }
    }
    
    /**
     * Log maintenance need
     */
    logMaintenanceNeed(characterId, reason) {
        // Could be used for notifications/reminders
        console.log(`Maintenance needed for ${characterId}: ${reason}`);
    }
    
    /**
     * Get harem summary
     */
    getHaremSummary() {
        const harem = this.stateManager.state.harem;
        
        const summary = {
            total_members: harem.members.length,
            members: [],
            total_abilities: 0,
            rebellion_risks: 0
        };
        
        harem.members.forEach(characterId => {
            const character = this.stateManager.getCharacter(characterId);
            const abilities = harem.abilities_unlocked[characterId] || [];
            const warnings = harem.rebellion_warnings[characterId] || { level: 0 };
            
            summary.members.push({
                id: characterId,
                name: character.name,
                loyalty: character.stats.loyalty,
                corruption: character.stats.corruption,
                abilities: abilities.length,
                rebellion_risk: warnings.level
            });
            
            summary.total_abilities += abilities.length;
            if (warnings.level > 0) summary.rebellion_risks++;
        });
        
        return summary;
    }
}
