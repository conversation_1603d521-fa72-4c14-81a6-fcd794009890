// User-Friendly Help System
// Provides clear guidance and tutorials for non-coders

export class HelpSystem {
    constructor() {
        this.helpContainer = null;
        this.setupHelpSystem();
        this.setupKeyboardShortcuts();
    }

    setupHelpSystem() {
        // Create help overlay
        this.helpContainer = document.createElement('div');
        this.helpContainer.id = 'help-system-container';
        this.helpContainer.className = 'help-system-container hidden';
        this.helpContainer.innerHTML = `
            <div class="help-modal">
                <div class="help-header">
                    <div class="help-title">[SYSTEM HELP & GUIDANCE]</div>
                    <button class="help-close" onclick="helpSystem.hideHelp()">×</button>
                </div>
                <div class="help-content">
                    <div class="help-tabs">
                        <button class="help-tab active" onclick="helpSystem.showTab('basics')">Game Basics</button>
                        <button class="help-tab" onclick="helpSystem.showTab('troubleshooting')">Troubleshooting</button>
                        <button class="help-tab" onclick="helpSystem.showTab('shortcuts')">Shortcuts</button>
                        <button class="help-tab" onclick="helpSystem.showTab('advanced')">Advanced</button>
                    </div>
                    <div class="help-tab-content" id="help-tab-content">
                        <!-- Content will be populated by showTab() -->
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(this.helpContainer);
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // F1 or ? to show help
            if (e.key === 'F1' || (e.key === '?' && !e.ctrlKey && !e.altKey)) {
                e.preventDefault();
                this.showHelp('basics');
            }
            
            // Escape to close help
            if (e.key === 'Escape' && !this.helpContainer.classList.contains('hidden')) {
                this.hideHelp();
            }
        });
    }

    showHelp(tab = 'basics') {
        this.helpContainer.classList.remove('hidden');
        setTimeout(() => {
            this.helpContainer.classList.add('visible');
        }, 10);
        this.showTab(tab);
    }

    hideHelp() {
        this.helpContainer.classList.remove('visible');
        setTimeout(() => {
            this.helpContainer.classList.add('hidden');
        }, 300);
    }

    showTab(tabName) {
        // Update tab buttons
        const tabs = this.helpContainer.querySelectorAll('.help-tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        
        const activeTab = Array.from(tabs).find(tab => 
            tab.textContent.toLowerCase().includes(tabName.toLowerCase())
        );
        if (activeTab) activeTab.classList.add('active');

        // Update content
        const content = this.getTabContent(tabName);
        const contentDiv = this.helpContainer.querySelector('#help-tab-content');
        contentDiv.innerHTML = content;
    }

    getTabContent(tabName) {
        const contents = {
            'basics': `
                <h3>🎮 How to Play</h3>
                <div class="help-section">
                    <h4>Reading the Story</h4>
                    <ul>
                        <li>Read the story text carefully - it contains important clues</li>
                        <li>Character names in <span style="color: #d4af37;">gold</span> are clickable for profiles</li>
                        <li>Blue system prompts show game mechanics and rewards</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Making Choices</h4>
                    <ul>
                        <li>Click the golden choice buttons to progress</li>
                        <li>Choices affect your stats: CP, SP, and DXP</li>
                        <li>Some choices unlock new abilities or items</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Understanding Stats</h4>
                    <ul>
                        <li><strong>CP:</strong> Corruption Points - measure moral choices</li>
                        <li><strong>SP:</strong> System Points - currency for abilities</li>
                        <li><strong>DXP:</strong> Dominance Experience - power progression</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>System Menu</h4>
                    <ul>
                        <li>Click [SYSTEM] in the bottom bar to access menus</li>
                        <li>View inventory, skills, and other game features</li>
                        <li>Some features unlock as you progress</li>
                    </ul>
                </div>
            `,
            
            'troubleshooting': `
                <h3>🔧 Common Issues & Solutions</h3>
                
                <div class="help-section">
                    <h4>Game Won't Load</h4>
                    <ul>
                        <li>Make sure you're using the <code>run-game.bat</code> file (Windows)</li>
                        <li>Don't open <code>index.html</code> directly in browser</li>
                        <li>Check that Node.js is installed</li>
                        <li>Try refreshing the page (F5)</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Chapter Won't Load</h4>
                    <ul>
                        <li>The chapter might still be in development</li>
                        <li>Try the "Go Back" option in error dialogs</li>
                        <li>Check your internet connection</li>
                        <li>Clear browser cache (Ctrl+F5)</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Lost Progress</h4>
                    <ul>
                        <li>The game auto-saves every 30 seconds</li>
                        <li>Progress is saved when you make choices</li>
                        <li>Check if you have a save file to restore</li>
                        <li>Use the System menu to export/import saves</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Performance Issues</h4>
                    <ul>
                        <li>Close other browser tabs</li>
                        <li>Disable browser extensions</li>
                        <li>Try a different browser (Chrome recommended)</li>
                        <li>Restart the game server</li>
                    </ul>
                </div>
            `,
            
            'shortcuts': `
                <h3>⌨️ Keyboard Shortcuts</h3>
                
                <div class="help-section">
                    <h4>General Navigation</h4>
                    <ul>
                        <li><kbd>F1</kbd> or <kbd>?</kbd> - Show this help</li>
                        <li><kbd>Escape</kbd> - Close dialogs/help</li>
                        <li><kbd>F5</kbd> - Refresh page</li>
                        <li><kbd>Ctrl+F5</kbd> - Hard refresh (clear cache)</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Browser Controls</h4>
                    <ul>
                        <li><kbd>F12</kbd> - Open developer tools (for debugging)</li>
                        <li><kbd>Ctrl+Shift+I</kbd> - Alternative developer tools</li>
                        <li><kbd>Ctrl+R</kbd> - Reload page</li>
                        <li><kbd>Ctrl+W</kbd> - Close tab</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Accessibility</h4>
                    <ul>
                        <li><kbd>Tab</kbd> - Navigate between clickable elements</li>
                        <li><kbd>Enter</kbd> - Activate focused button</li>
                        <li><kbd>Ctrl +</kbd> - Zoom in</li>
                        <li><kbd>Ctrl -</kbd> - Zoom out</li>
                    </ul>
                </div>
            `,
            
            'advanced': `
                <h3>🔬 Advanced Features</h3>
                
                <div class="help-section">
                    <h4>Save Management</h4>
                    <ul>
                        <li>Game auto-saves progress automatically</li>
                        <li>Manual save: Use System menu options</li>
                        <li>Export saves: Download backup files</li>
                        <li>Import saves: Upload backup files</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Developer Tools (For Debugging)</h4>
                    <ul>
                        <li>Press F12 to open browser console</li>
                        <li>Look for red error messages</li>
                        <li>Check Network tab for loading issues</li>
                        <li>Console shows detailed error information</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>File Structure</h4>
                    <ul>
                        <li><code>story/</code> - Chapter files (JavaScript)</li>
                        <li><code>js/</code> - Game engine files</li>
                        <li><code>run-game.bat</code> - Windows launcher</li>
                        <li><code>package.json</code> - Node.js configuration</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Customization</h4>
                    <ul>
                        <li>Edit <code>style.css</code> for visual changes</li>
                        <li>Modify chapter files to change story</li>
                        <li>Player stats in <code>js/player.js</code></li>
                        <li>Always backup before making changes!</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>Emergency Recovery</h4>
                    <ul>
                        <li>Type <code>gameState.resetToSafeState()</code> in console</li>
                        <li>Type <code>gameState.deleteSave()</code> to clear saves</li>
                        <li>Type <code>location.reload()</code> to restart</li>
                        <li>Delete browser data to completely reset</li>
                    </ul>
                </div>
            `
        };

        return contents[tabName] || contents['basics'];
    }

    showQuickTip(message, duration = 5000) {
        const tip = document.createElement('div');
        tip.className = 'quick-tip';
        tip.innerHTML = `
            <div class="tip-content">
                💡 <strong>Tip:</strong> ${message}
            </div>
        `;
        
        document.body.appendChild(tip);
        
        setTimeout(() => tip.classList.add('visible'), 10);
        
        setTimeout(() => {
            tip.classList.remove('visible');
            setTimeout(() => {
                if (tip.parentNode) {
                    document.body.removeChild(tip);
                }
            }, 300);
        }, duration);
    }

    showFirstTimeHelp() {
        // Show help for first-time users
        const hasSeenHelp = localStorage.getItem('predator_ascent_seen_help');
        
        if (!hasSeenHelp) {
            setTimeout(() => {
                this.showQuickTip('Press F1 or ? for help anytime! This game auto-saves your progress.', 8000);
                localStorage.setItem('predator_ascent_seen_help', 'true');
            }, 3000);
        }
    }
}

// Create global instance
export const helpSystem = new HelpSystem();

// Make it globally accessible
window.helpSystem = helpSystem;
