// Emergency Recovery Tools for Non-Coders
// Simple tools to fix common issues without technical knowledge

export class EmergencyTools {
    constructor() {
        this.setupEmergencyAccess();
    }

    setupEmergencyAccess() {
        // Secret key combination to access emergency tools
        let keySequence = [];
        const emergencySequence = ['e', 'm', 'e', 'r', 'g', 'e', 'n', 'c', 'y'];
        
        document.addEventListener('keydown', (e) => {
            keySequence.push(e.key.toLowerCase());
            
            // Keep only the last 9 keys
            if (keySequence.length > emergencySequence.length) {
                keySequence.shift();
            }
            
            // Check if emergency sequence was typed
            if (keySequence.join('') === emergencySequence.join('')) {
                this.showEmergencyPanel();
                keySequence = []; // Reset sequence
            }
        });
    }

    showEmergencyPanel() {
        const panel = document.createElement('div');
        panel.id = 'emergency-panel';
        panel.className = 'emergency-panel';
        panel.innerHTML = `
            <div class="emergency-modal">
                <div class="emergency-header">
                    <div class="emergency-title">🚨 EMERGENCY RECOVERY TOOLS</div>
                    <button class="emergency-close" onclick="emergencyTools.hidePanel()">×</button>
                </div>
                <div class="emergency-content">
                    <p><strong>⚠️ Use these tools only if the game is broken!</strong></p>
                    <p>These are safe, one-click fixes for common problems:</p>
                    
                    <div class="emergency-tools-grid">
                        <button class="emergency-tool" onclick="emergencyTools.fixCorruptedSave()">
                            🔧 Fix Corrupted Save
                        </button>
                        <button class="emergency-tool" onclick="emergencyTools.clearAllData()">
                            🗑️ Clear All Data
                        </button>
                        <button class="emergency-tool" onclick="emergencyTools.resetToChapter1()">
                            🔄 Reset to Chapter 1
                        </button>
                        <button class="emergency-tool" onclick="emergencyTools.fixLoadingIssues()">
                            ⚡ Fix Loading Issues
                        </button>
                        <button class="emergency-tool" onclick="emergencyTools.exportDebugInfo()">
                            📋 Export Debug Info
                        </button>
                        <button class="emergency-tool" onclick="emergencyTools.testGameSystems()">
                            🧪 Test Game Systems
                        </button>
                    </div>
                    
                    <div class="emergency-info">
                        <h4>How to access this panel:</h4>
                        <p>Type "emergency" anywhere in the game (no quotes, just the word)</p>
                        
                        <h4>Safe to use:</h4>
                        <p>✅ All tools create backups before making changes</p>
                        <p>✅ You can always restart the game if something goes wrong</p>
                        <p>✅ Your browser data won't be harmed</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        setTimeout(() => panel.classList.add('visible'), 10);
    }

    hidePanel() {
        const panel = document.getElementById('emergency-panel');
        if (panel) {
            panel.classList.remove('visible');
            setTimeout(() => {
                if (panel.parentNode) {
                    document.body.removeChild(panel);
                }
            }, 300);
        }
    }

    fixCorruptedSave() {
        this.showProgress('Fixing corrupted save data...');
        
        try {
            // Create backup first
            const currentSave = localStorage.getItem('predator_ascent_save');
            if (currentSave) {
                localStorage.setItem('predator_ascent_save_emergency_backup', currentSave);
            }
            
            // Try to recover from backup or reset to safe state
            if (window.gameState) {
                window.gameState.recoverFromCorruption();
            }
            
            this.showSuccess('✅ Save data has been repaired! The game should work normally now.');
        } catch (error) {
            this.showError('❌ Could not fix save data. Try "Clear All Data" instead.');
        }
    }

    clearAllData() {
        if (confirm('⚠️ This will delete ALL game data and start fresh. Are you sure?')) {
            this.showProgress('Clearing all game data...');
            
            try {
                // Clear all game-related localStorage
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.includes('predator_ascent')) {
                        localStorage.removeItem(key);
                    }
                });
                
                this.showSuccess('✅ All data cleared! Refresh the page to start fresh.');
            } catch (error) {
                this.showError('❌ Could not clear data. Try refreshing the page manually.');
            }
        }
    }

    resetToChapter1() {
        this.showProgress('Resetting game to Chapter 1...');
        
        try {
            if (window.gameState) {
                window.gameState.resetToSafeState();
            }
            
            if (window.main) {
                window.main.startNewGame();
            }
            
            this.hidePanel();
            this.showSuccess('✅ Game reset to Chapter 1! You can start playing again.');
        } catch (error) {
            this.showError('❌ Could not reset game. Try refreshing the page.');
        }
    }

    fixLoadingIssues() {
        this.showProgress('Fixing loading issues...');
        
        try {
            // Clear any cached modules
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    registrations.forEach(registration => registration.unregister());
                });
            }
            
            // Clear browser cache for this site
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            
            this.showSuccess('✅ Loading issues fixed! Refresh the page to see improvements.');
        } catch (error) {
            this.showError('❌ Could not fix loading issues. Try a hard refresh (Ctrl+F5).');
        }
    }

    exportDebugInfo() {
        this.showProgress('Collecting debug information...');
        
        try {
            const debugInfo = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                gameState: window.gameState?.getSaveInfo() || 'No save data',
                currentChapter: window.main?.currentChapterNumber || 'Unknown',
                localStorage: this.getLocalStorageInfo(),
                errors: this.getRecentErrors(),
                gameVersion: '1.0'
            };
            
            const blob = new Blob([JSON.stringify(debugInfo, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `predator_ascent_debug_${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showSuccess('✅ Debug info exported! Send this file when reporting issues.');
        } catch (error) {
            this.showError('❌ Could not export debug info.');
        }
    }

    testGameSystems() {
        this.showProgress('Testing game systems...');
        
        const tests = [
            { name: 'Local Storage', test: () => localStorage.setItem('test', 'test') && localStorage.removeItem('test') },
            { name: 'Game State Manager', test: () => window.gameState !== undefined },
            { name: 'Error Handler', test: () => window.gameErrorHandler !== undefined },
            { name: 'Help System', test: () => window.helpSystem !== undefined },
            { name: 'UI Functions', test: () => window.ui !== undefined },
            { name: 'Main Game Functions', test: () => window.main !== undefined }
        ];
        
        const results = tests.map(test => {
            try {
                const passed = test.test();
                return `${passed ? '✅' : '❌'} ${test.name}: ${passed ? 'OK' : 'FAILED'}`;
            } catch (error) {
                return `❌ ${test.name}: ERROR (${error.message})`;
            }
        });
        
        const resultText = results.join('\n');
        this.showInfo(`🧪 System Test Results:\n\n${resultText}`);
    }

    getLocalStorageInfo() {
        try {
            const info = {};
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.includes('predator_ascent')) {
                    info[key] = localStorage.getItem(key)?.length || 0;
                }
            });
            return info;
        } catch (error) {
            return { error: error.message };
        }
    }

    getRecentErrors() {
        // This would collect errors from console if we had error logging
        return 'Error logging not implemented';
    }

    showProgress(message) {
        this.updatePanelContent(`
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 2em; margin-bottom: 20px;">⏳</div>
                <p>${message}</p>
            </div>
        `);
    }

    showSuccess(message) {
        this.updatePanelContent(`
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 2em; margin-bottom: 20px;">✅</div>
                <p>${message}</p>
                <button onclick="emergencyTools.hidePanel()" style="margin-top: 20px; padding: 10px 20px; background: #4ade80; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `);
    }

    showError(message) {
        this.updatePanelContent(`
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 2em; margin-bottom: 20px;">❌</div>
                <p>${message}</p>
                <button onclick="emergencyTools.hidePanel()" style="margin-top: 20px; padding: 10px 20px; background: #ff6b6b; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `);
    }

    showInfo(message) {
        this.updatePanelContent(`
            <div style="padding: 20px;">
                <pre style="background: rgba(0,0,0,0.5); padding: 20px; border-radius: 5px; white-space: pre-wrap; font-family: monospace; font-size: 0.9em;">${message}</pre>
                <button onclick="emergencyTools.hidePanel()" style="margin-top: 20px; padding: 10px 20px; background: #d4af37; color: black; border: none; border-radius: 5px; cursor: pointer;">Close</button>
            </div>
        `);
    }

    updatePanelContent(content) {
        const panel = document.getElementById('emergency-panel');
        if (panel) {
            const contentDiv = panel.querySelector('.emergency-content');
            if (contentDiv) {
                contentDiv.innerHTML = content;
            }
        }
    }
}

// Create global instance
export const emergencyTools = new EmergencyTools();

// Make it globally accessible
window.emergencyTools = emergencyTools;
