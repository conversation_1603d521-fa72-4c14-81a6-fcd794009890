/**
 * Financial System - Manages wealth, investments, and CP conversion
 */

export const financialData = {
    // Conversion rates
    conversion_rates: {
        wealth_to_cp: {
            rate: 100000, // 1 Lakh = 100 CP
            weekly_limit: 5, // Max 5 conversions per week
            cooldown: 24 * 60 * 60 * 1000 // 24 hours between conversions
        },
        cp_to_sp: {
            rate: 100, // 100 CP = 1 SP
            no_limit: true
        }
    },
    
    // Investment options
    investments: {
        'stocks': {
            name: 'Stock Market',
            description: 'Invest in blue-chip stocks for steady returns',
            min_investment: 1000000, // 10 Lakh minimum
            max_investment: 50000000, // 5 Crore maximum
            return_rate: 0.12, // 12% annual return
            risk_level: 'medium',
            liquidity: 'high', // Can withdraw anytime
            unlock_requirements: []
        },
        
        'real_estate': {
            name: 'Real Estate',
            description: 'Buy properties for rental income and appreciation',
            min_investment: 5000000, // 50 Lakh minimum
            max_investment: 200000000, // 20 Crore maximum
            return_rate: 0.15, // 15% annual return
            risk_level: 'low',
            liquidity: 'medium', // Takes time to sell
            unlock_requirements: ['wealth_display']
        },
        
        'business_ventures': {
            name: 'Business Ventures',
            description: 'Invest in startups and business opportunities',
            min_investment: 2000000, // 20 Lakh minimum
            max_investment: 100000000, // 10 Crore maximum
            return_rate: 0.25, // 25% annual return (high risk, high reward)
            risk_level: 'high',
            liquidity: 'low', // Long-term commitment
            unlock_requirements: ['business_connections']
        },
        
        'corruption_enterprises': {
            name: 'Corruption Enterprises',
            description: 'Fund illegal activities for massive returns',
            min_investment: 10000000, // 1 Crore minimum
            max_investment: 500000000, // 50 Crore maximum
            return_rate: 0.50, // 50% annual return (very high risk)
            risk_level: 'extreme',
            liquidity: 'very_low', // Hard to exit
            unlock_requirements: ['corruption_network', 'high_cp']
        }
    },
    
    // Bribery and influence costs
    bribery_costs: {
        'police_officer': {
            base_cost: 50000,
            description: 'Bribe local police to ignore activities',
            duration: '1 month',
            effects: ['reduced_suspicion', 'crime_immunity']
        },
        
        'school_official': {
            base_cost: 100000,
            description: 'Bribe school officials for access and information',
            duration: '3 months',
            effects: ['school_access', 'student_records', 'private_meetings']
        },
        
        'government_clerk': {
            base_cost: 200000,
            description: 'Bribe government officials for documents and favors',
            duration: '6 months',
            effects: ['document_access', 'permit_approval', 'background_checks']
        },
        
        'judge': {
            base_cost: 1000000,
            description: 'Bribe judges for legal immunity',
            duration: '1 year',
            effects: ['legal_immunity', 'case_dismissal', 'reduced_sentences']
        }
    },
    
    // Luxury purchases for corruption
    luxury_items: {
        'designer_clothes': {
            cost: 500000,
            description: 'High-end designer clothing for targets',
            corruption_bonus: 10,
            target_types: ['fashion_conscious', 'status_seeking']
        },
        
        'jewelry': {
            cost: 1000000,
            description: 'Expensive jewelry as gifts',
            corruption_bonus: 15,
            target_types: ['materialistic', 'validation_seeking']
        },
        
        'luxury_car': {
            cost: 5000000,
            description: 'Luxury car for impressive displays of wealth',
            corruption_bonus: 25,
            target_types: ['all'],
            permanent: true
        },
        
        'private_apartment': {
            cost: 10000000,
            description: 'Private apartment for secret meetings',
            corruption_bonus: 30,
            target_types: ['all'],
            permanent: true,
            unlocks: ['private_location']
        }
    }
};

/**
 * Financial Manager - Handles all financial operations
 */
export class FinancialManager {
    constructor(stateManager, gameEngine) {
        this.stateManager = stateManager;
        this.gameEngine = gameEngine;
        this.financialData = financialData;
        
        // Initialize financial state if not exists
        this.initializeFinancialState();
    }
    
    /**
     * Initialize financial state
     */
    initializeFinancialState() {
        const player = this.stateManager.getPlayer();
        
        if (!player.financial) {
            player.financial = {
                investments: {},
                bribes_active: {},
                luxury_items: [],
                conversion_history: [],
                weekly_conversions: 0,
                last_conversion: null,
                last_week_reset: Date.now()
            };
        }
    }
    
    /**
     * Convert wealth to CP
     */
    convertWealthToCP(amount = 1) {
        const player = this.stateManager.getPlayer();
        const conversionData = this.financialData.conversion_rates.wealth_to_cp;
        const totalCost = conversionData.rate * amount;
        const cpGain = 100 * amount;
        
        // Check if player has enough wealth
        if (player.stats.wealth < totalCost) {
            return {
                success: false,
                message: 'Insufficient wealth for conversion',
                required: totalCost,
                available: player.stats.wealth
            };
        }
        
        // Check weekly limit
        this.checkWeeklyReset();
        if (player.financial.weekly_conversions >= conversionData.weekly_limit) {
            return {
                success: false,
                message: `Weekly conversion limit reached (${conversionData.weekly_limit})`
            };
        }
        
        // Check cooldown
        if (player.financial.last_conversion && 
            Date.now() - player.financial.last_conversion < conversionData.cooldown) {
            const remainingTime = conversionData.cooldown - (Date.now() - player.financial.last_conversion);
            return {
                success: false,
                message: `Conversion on cooldown`,
                remaining_time: Math.ceil(remainingTime / (60 * 60 * 1000)) // hours
            };
        }
        
        // Perform conversion
        player.stats.wealth -= totalCost;
        player.stats.cp += cpGain;
        player.financial.weekly_conversions++;
        player.financial.last_conversion = Date.now();
        
        // Record conversion
        player.financial.conversion_history.push({
            timestamp: Date.now(),
            wealth_spent: totalCost,
            cp_gained: cpGain,
            type: 'wealth_to_cp'
        });
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Converted ₹${totalCost.toLocaleString()} to ${cpGain} CP`,
            cp_gained: cpGain,
            wealth_spent: totalCost
        };
    }
    
    /**
     * Check and reset weekly conversion limit
     */
    checkWeeklyReset() {
        const player = this.stateManager.getPlayer();
        const weekInMs = 7 * 24 * 60 * 60 * 1000;
        
        if (Date.now() - player.financial.last_week_reset > weekInMs) {
            player.financial.weekly_conversions = 0;
            player.financial.last_week_reset = Date.now();
        }
    }
    
    /**
     * Make investment
     */
    makeInvestment(investmentType, amount) {
        const player = this.stateManager.getPlayer();
        const investmentData = this.financialData.investments[investmentType];
        
        if (!investmentData) {
            return { success: false, message: 'Invalid investment type' };
        }
        
        // Check requirements
        if (!this.checkInvestmentRequirements(investmentType)) {
            return { success: false, message: 'Investment requirements not met' };
        }
        
        // Check amount limits
        if (amount < investmentData.min_investment) {
            return { 
                success: false, 
                message: `Minimum investment: ₹${investmentData.min_investment.toLocaleString()}` 
            };
        }
        
        if (amount > investmentData.max_investment) {
            return { 
                success: false, 
                message: `Maximum investment: ₹${investmentData.max_investment.toLocaleString()}` 
            };
        }
        
        // Check if player has enough wealth
        if (player.stats.wealth < amount) {
            return { success: false, message: 'Insufficient wealth' };
        }
        
        // Make investment
        player.stats.wealth -= amount;
        
        if (!player.financial.investments[investmentType]) {
            player.financial.investments[investmentType] = {
                total_invested: 0,
                current_value: 0,
                last_update: Date.now(),
                transactions: []
            };
        }
        
        const investment = player.financial.investments[investmentType];
        investment.total_invested += amount;
        investment.current_value += amount;
        investment.transactions.push({
            type: 'investment',
            amount: amount,
            timestamp: Date.now()
        });
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Invested ₹${amount.toLocaleString()} in ${investmentData.name}`,
            investment_type: investmentType,
            amount: amount
        };
    }
    
    /**
     * Check investment requirements
     */
    checkInvestmentRequirements(investmentType) {
        const investmentData = this.financialData.investments[investmentType];
        const player = this.stateManager.getPlayer();
        
        return investmentData.unlock_requirements.every(requirement => {
            switch (requirement) {
                case 'wealth_display':
                    return player.stats.wealth >= 50000000; // 5 Crore
                case 'business_connections':
                    return player.traits?.includes('Business Network');
                case 'corruption_network':
                    return player.stats.cp >= 500;
                case 'high_cp':
                    return player.stats.cp >= 1000;
                default:
                    return true;
            }
        });
    }
    
    /**
     * Update investment values (called periodically)
     */
    updateInvestmentValues() {
        const player = this.stateManager.getPlayer();
        const now = Date.now();
        
        Object.keys(player.financial.investments).forEach(investmentType => {
            const investment = player.financial.investments[investmentType];
            const investmentData = this.financialData.investments[investmentType];
            
            if (!investmentData) return;
            
            const timeDiff = now - investment.last_update;
            const daysPassed = timeDiff / (24 * 60 * 60 * 1000);
            
            if (daysPassed >= 1) {
                // Calculate daily return
                const dailyReturn = investmentData.return_rate / 365;
                const returnAmount = investment.current_value * dailyReturn * Math.floor(daysPassed);
                
                investment.current_value += returnAmount;
                investment.last_update = now;
                
                // Add transaction record
                if (returnAmount > 0) {
                    investment.transactions.push({
                        type: 'return',
                        amount: returnAmount,
                        timestamp: now
                    });
                }
            }
        });
        
        this.stateManager.triggerAutoSave();
    }
    
    /**
     * Withdraw from investment
     */
    withdrawInvestment(investmentType, amount) {
        const player = this.stateManager.getPlayer();
        const investment = player.financial.investments[investmentType];
        const investmentData = this.financialData.investments[investmentType];
        
        if (!investment || !investmentData) {
            return { success: false, message: 'Investment not found' };
        }
        
        if (amount > investment.current_value) {
            return { success: false, message: 'Insufficient investment value' };
        }
        
        // Apply liquidity penalty based on investment type
        let penalty = 0;
        switch (investmentData.liquidity) {
            case 'medium': penalty = 0.05; break; // 5% penalty
            case 'low': penalty = 0.10; break; // 10% penalty
            case 'very_low': penalty = 0.20; break; // 20% penalty
        }
        
        const penaltyAmount = amount * penalty;
        const withdrawAmount = amount - penaltyAmount;
        
        // Update investment
        investment.current_value -= amount;
        investment.transactions.push({
            type: 'withdrawal',
            amount: amount,
            penalty: penaltyAmount,
            received: withdrawAmount,
            timestamp: Date.now()
        });
        
        // Add to player wealth
        player.stats.wealth += withdrawAmount;
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Withdrew ₹${withdrawAmount.toLocaleString()} (₹${penaltyAmount.toLocaleString()} penalty)`,
            amount: withdrawAmount,
            penalty: penaltyAmount
        };
    }
    
    /**
     * Purchase luxury item
     */
    purchaseLuxuryItem(itemId) {
        const player = this.stateManager.getPlayer();
        const itemData = this.financialData.luxury_items[itemId];
        
        if (!itemData) {
            return { success: false, message: 'Item not found' };
        }
        
        if (player.stats.wealth < itemData.cost) {
            return { success: false, message: 'Insufficient wealth' };
        }
        
        // Check if already owned (for permanent items)
        if (itemData.permanent && player.financial.luxury_items.includes(itemId)) {
            return { success: false, message: 'Already owned' };
        }
        
        // Purchase item
        player.stats.wealth -= itemData.cost;
        player.financial.luxury_items.push({
            id: itemId,
            purchased: Date.now(),
            cost: itemData.cost
        });
        
        // Apply permanent effects
        if (itemData.unlocks) {
            itemData.unlocks.forEach(unlock => {
                // Handle unlocks (e.g., new locations, abilities)
                this.gameEngine.emit('luxury:unlock', { unlock, itemId });
            });
        }
        
        this.stateManager.triggerAutoSave();
        
        return {
            success: true,
            message: `Purchased ${itemData.description}`,
            item: itemData,
            corruption_bonus: itemData.corruption_bonus
        };
    }
    
    /**
     * Get financial summary
     */
    getFinancialSummary() {
        const player = this.stateManager.getPlayer();
        
        let totalInvestmentValue = 0;
        Object.values(player.financial.investments).forEach(investment => {
            totalInvestmentValue += investment.current_value;
        });
        
        return {
            liquid_wealth: player.stats.wealth,
            total_investments: totalInvestmentValue,
            total_net_worth: player.stats.wealth + totalInvestmentValue,
            weekly_conversions_remaining: this.financialData.conversion_rates.wealth_to_cp.weekly_limit - player.financial.weekly_conversions,
            luxury_items_owned: player.financial.luxury_items.length
        };
    }
}
