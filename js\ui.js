import { player } from './player.js';
// The import from main.js has been REMOVED.

const storyContainer = document.getElementById('story-container');
const hudElements = {
    location: document.getElementById('hud-location'),
    time: document.getElementById('hud-time'),
    cp: document.getElementById('hud-cp'),
    sp: document.getElementById('hud-sp'),
    dxp: document.getElementById('hud-dxp'),
};
const modalContainer = document.getElementById('modal-container');
const hudContainer = document.getElementById('hud');

let currentModals = {};

export function initializeUI() {
    const systemButton = document.createElement('div');
    systemButton.className = 'hud-item system-button';
    systemButton.innerHTML = `<strong>[SYSTEM]</strong>`;
    systemButton.onclick = () => showSystemMenu();
    hudContainer.appendChild(systemButton);

    // Add help button
    const helpButton = document.createElement('div');
    helpButton.className = 'hud-item system-button';
    helpButton.innerHTML = `<strong>[HELP]</strong>`;
    helpButton.onclick = () => window.helpSystem?.showHelp('basics');
    helpButton.title = 'Press F1 or ? for help';
    hudContainer.appendChild(helpButton);

    // Show first-time help
    setTimeout(() => {
        if (window.helpSystem) {
            window.helpSystem.showFirstTimeHelp();
        }
    }, 2000);
}

export function updateHUD() {
    hudElements.location.textContent = player.location;
    hudElements.time.textContent = player.time;
    hudElements.cp.textContent = player.stats.cp;
    hudElements.sp.textContent = player.stats.sp;
    hudElements.dxp.textContent = player.stats.dxp;
}

// renderScene now accepts a callback function `onChoiceMade`
export function renderScene(sceneId, sceneData, onChoiceMade) {
    if (!sceneData) {
        console.error(`Scene data not found for sceneId: ${sceneId}`);
        return;
    }

    storyContainer.style.opacity = '0';
    storyContainer.style.transform = 'translateY(20px)';

    setTimeout(() => {
        if (sceneData.onLoad) {
            sceneData.onLoad();
        }

        let html = `<div id="${sceneId}" class="scene-content">${sceneData.text}</div>`;

        if (sceneData.choices && sceneData.choices.length > 0) {
            html += `<div class="choice-section">`;
            sceneData.choices.forEach((choice, index) => {
                html += `<a href="#" class="choice-button" data-choice-index="${index}">${choice.label}</a>`;
            });
            html += `</div>`;
        }

        storyContainer.innerHTML = html;
        // Pass the onChoiceMade callback to the listener setup function
        addChoiceListeners(sceneData.choices, onChoiceMade);
        updateHUD();

        storyContainer.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        storyContainer.style.opacity = '1';
        storyContainer.style.transform = 'translateY(0)';

        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 150);
}

// addChoiceListeners now accepts and uses the callback
function addChoiceListeners(choices, onChoiceMade) {
    if (!choices) return;
    const buttons = document.querySelectorAll('.choice-button');
    buttons.forEach((button, index) => {
        button.style.opacity = '0';
        button.style.transform = 'translateY(20px)';
        setTimeout(() => {
            button.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, 300 + (index * 100));

        button.addEventListener('click', (event) => {
            event.preventDefault();
            button.style.transform = 'scale(0.95)';
            setTimeout(() => { button.style.transform = 'scale(1)'; }, 150);
            buttons.forEach(btn => {
                btn.style.pointerEvents = 'none';
                btn.style.opacity = '0.6';
            });
            setTimeout(() => {
                // Instead of calling an imported function, it calls the function that was passed to it.
                onChoiceMade(choices[index]);
            }, 200);
        });
    });
}

export function showModal(modalId) {
    const modalData = currentModals[modalId];
    if (!modalData) return;
    renderModal(modalId, modalData.title, modalData.content);
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId + 'Modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.style.display = 'none';
            modalContainer.innerHTML = '';
        }, 290);
    } else {
        modalContainer.innerHTML = '';
    }
}

export function setCurrentModals(modals) {
    currentModals = modals;
}

function renderModal(id, title, content) {
    let modalHTML = `
        <div id="${id}Modal" class="modal" style="display:block;">
            <div class="modal-content">
                <span class="close-button" onclick="window.ui.closeModal('${id}')">×</span>
                <div class="modal-header">${title}</div>
                <div class="modal-body">${content}</div>
            </div>
        </div>
    `;
    modalContainer.innerHTML = modalHTML;
    const modal = document.getElementById(id + 'Modal');
    modal.addEventListener('click', (event) => {
        if (event.target === modal) { closeModal(id); }
    });
    document.addEventListener('keydown', function escapeHandler(event) {
        if (event.key === 'Escape') {
            closeModal(id);
            document.removeEventListener('keydown', escapeHandler);
        }
    });
}

// --- System Menu Functions ---
function showSystemMenu() {
    const saveInfo = window.gameState?.getSaveInfo();
    const saveStatus = saveInfo ?
        `Last saved: ${saveInfo.timeAgo}` :
        'No save data found';

    const content = `
        <div class="system-menu-grid">
            <div class="system-menu-button" onclick="window.ui.showInventory()">[ Inventory ]</div>
            <div class="system-menu-button" onclick="window.ui.showSkills()">[ Skills ]</div>
            <div class="system-menu-button" onclick="window.ui.showSaveMenu()">[ Save/Load ]</div>
            <div class="system-menu-button" onclick="window.helpSystem.showHelp('basics')">[ Help & Guide ]</div>
            <div class="system-menu-button disabled">[ System Shop ]</div>
            <div class="system-menu-button disabled">[ Wealth Conversion ]</div>
        </div>
        <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; text-align: center; color: #888; font-size: 0.9em;">
            💾 ${saveStatus}
        </div>
    `;
    renderModal('systemMenu', '[ System Mainframe ]', content);
}

function showInventory() {
    let content = '<ul>';
    if (player.inventory.length === 0) {
        content += '<li>Your inventory is empty.</li>';
    } else {
        player.inventory.forEach(item => {
            content += `<li>${item.item} (x${item.quantity})</li>`;
        });
    }
    content += '</ul>';
    renderModal('inventory', '[ Player Inventory ]', content);
}

function showSkills() {
    let content = '<ul>';
    if (player.skills.length === 0) {
        content += '<li>You have no skills.</li>';
    } else {
        player.skills.forEach(skill => {
            content += `<li>${skill}</li>`;
        });
    }
    content += '</ul>';
    renderModal('skills', '[ Unlocked Skills ]', content);
}

function showSaveMenu() {
    const saveInfo = window.gameState?.getSaveInfo();
    const saveExists = saveInfo !== null;

    const content = `
        <div class="system-menu-grid">
            <div class="system-menu-button" onclick="window.gameState.save(); window.ui.closeModal();">[ Manual Save ]</div>
            <div class="system-menu-button ${!saveExists ? 'disabled' : ''}"
                 onclick="${saveExists ? 'window.main.continueGame(); window.ui.closeModal();' : ''}">
                [ Load Game ]
            </div>
            <div class="system-menu-button" onclick="window.gameState.exportSave()">[ Export Save ]</div>
            <div class="system-menu-button" onclick="window.ui.showImportSave()">[ Import Save ]</div>
            <div class="system-menu-button" onclick="window.ui.showResetConfirm()">[ Reset Game ]</div>
        </div>
        ${saveExists ? `
            <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px;">
                <h4 style="color: #d4af37; margin-bottom: 10px;">Current Save:</h4>
                <p><strong>Chapter:</strong> ${saveInfo.chapter}</p>
                <p><strong>Last played:</strong> ${saveInfo.timeAgo}</p>
                <p><strong>Stats:</strong> CP: ${saveInfo.playerStats.cp || 0}, SP: ${saveInfo.playerStats.sp || 0}, DXP: ${saveInfo.playerStats.dxp || 0}</p>
            </div>
        ` : `
            <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; text-align: center; color: #888;">
                No save data found. Game auto-saves your progress!
            </div>
        `}
    `;
    renderModal('saveMenu', '[ Save & Load Manager ]', content);
}

function showImportSave() {
    const content = `
        <div style="text-align: center; padding: 20px;">
            <p>Select a save file to import:</p>
            <input type="file" id="save-file-input" accept=".json" style="margin: 20px 0; padding: 10px; background: rgba(255,255,255,0.1); border: 1px solid #d4af37; border-radius: 5px; color: white;">
            <br>
            <button onclick="window.ui.importSaveFile()" style="background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%); color: #000; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; cursor: pointer; margin: 10px;">Import Save</button>
            <button onclick="window.ui.closeModal()" style="background: rgba(255,255,255,0.1); color: white; border: 1px solid #666; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin: 10px;">Cancel</button>
        </div>
    `;
    renderModal('importSave', '[ Import Save File ]', content);
}

function importSaveFile() {
    const fileInput = document.getElementById('save-file-input');
    const file = fileInput.files[0];

    if (!file) {
        window.gameErrorHandler?.handleError('save-error', 'Please select a file first');
        return;
    }

    window.gameState.importSave(file)
        .then(() => {
            closeModal();
            window.gameErrorHandler?.showSuccess('Save imported successfully! Restart to apply changes.');
        })
        .catch((error) => {
            window.gameErrorHandler?.handleError('save-error', error.message);
        });
}

function showResetConfirm() {
    const content = `
        <div style="text-align: center; padding: 20px;">
            <h3 style="color: #ff6b6b; margin-bottom: 20px;">⚠️ Reset Game</h3>
            <p>This will delete all your progress and start a new game.</p>
            <p><strong>This action cannot be undone!</strong></p>
            <div style="margin-top: 30px;">
                <button onclick="window.ui.confirmReset()" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; cursor: pointer; margin: 10px;">Yes, Reset Game</button>
                <button onclick="window.ui.closeModal()" style="background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%); color: #000; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; cursor: pointer; margin: 10px;">Cancel</button>
            </div>
        </div>
    `;
    renderModal('resetConfirm', '[ Confirm Reset ]', content);
}

function confirmReset() {
    window.gameState?.deleteSave();
    window.gameState?.resetToSafeState();
    closeModal();
    window.location.reload();
}

// Expose functions to global window object
window.ui = { ...window.ui, showSystemMenu, showInventory, showSkills, showSaveMenu, showImportSave, importSaveFile, showResetConfirm, confirmReset };