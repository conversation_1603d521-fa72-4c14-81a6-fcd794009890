@echo off
title The Predator's Ascent - Game Server
echo.
echo ========================================
echo   The Predator's Ascent - Game Server
echo ========================================
echo.
echo Starting the game server...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check if npm dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

REM Start the server
echo Starting server on http://localhost:8000
echo.
echo The game will open in your default browser automatically.
echo Press Ctrl+C to stop the server when you're done playing.
echo.
npm start

REM If npm start fails, try alternative method
if %errorlevel% neq 0 (
    echo.
    echo Trying alternative server method...
    npx http-server -p 8000 -o
)

echo.
echo Server stopped. Press any key to exit.
pause >nul
