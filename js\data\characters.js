/**
 * Character Data - Detailed profiles for all characters in the corruption simulator
 */

export const characterData = {
    // Family Members
    sonia: {
        name: '<PERSON>',
        relationship: 'Mother',
        age: 42,
        description: 'Your widowed mother. A beautiful MILF struggling with loneliness and financial stress after your father\'s death.',
        
        stats: {
            lust: 5,
            corruption: 0,
            loyalty: 80,
            fear: 0,
            love: 90,
            suspicion: 0,
            resistance: 60
        },
        
        physical: {
            height: '5\'6"',
            build: 'Voluptuous MILF',
            measurements: '38D-32-40',
            hair: 'Long black hair',
            eyes: 'Dark brown',
            skin: 'Fair complexion',
            notable: 'Heavy sagging breasts, wide birthing hips, soft belly from motherhood'
        },
        
        personality: {
            traits: ['Caring', 'Traditional', 'Vulnerable', 'Lonely', 'Protective'],
            likes: ['Family time', 'Cooking', 'Religious activities', 'Bollywood movies'],
            dislikes: ['Conflict', 'Financial stress', 'Being alone', 'Inappropriate behavior'],
            fears: ['Losing family', 'Financial ruin', 'Social judgment'],
            desires: ['Security', 'Companionship', 'Being needed', 'Physical affection']
        },
        
        vulnerabilities: [
            'Sexual starvation after years of widowhood',
            'Financial dependence and stress',
            'Deep loneliness and need for male attention',
            'Guilt over having physical desires',
            'Fear of family breaking apart'
        ],
        
        corruption_paths: {
            'gentle_seduction': {
                name: 'The Loving Son',
                description: 'Slowly corrupt through emotional manipulation and care',
                stages: ['Comfort', 'Emotional Dependency', 'Physical Boundaries', 'Intimate Touch', 'Complete Submission'],
                difficulty: 'Medium',
                time_required: '2-3 weeks'
            },
            'financial_control': {
                name: 'The Provider',
                description: 'Use wealth to create dependency and obligation',
                stages: ['Financial Relief', 'Luxury Addiction', 'Debt Creation', 'Payment Demands', 'Sexual Servitude'],
                difficulty: 'Easy',
                time_required: '1-2 weeks'
            },
            'blackmail': {
                name: 'The Predator',
                description: 'Discover secrets and use them for control',
                stages: ['Secret Discovery', 'Evidence Gathering', 'Confrontation', 'Demands', 'Complete Control'],
                difficulty: 'Hard',
                time_required: '3-4 weeks'
            }
        },
        
        schedule_base: {
            'Monday': { 'Morning': 'Home', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Tuesday': { 'Morning': 'Home', 'Afternoon': 'Market', 'Evening': 'Temple', 'Night': 'Home' },
            'Wednesday': { 'Morning': 'Home', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Thursday': { 'Morning': 'Home', 'Afternoon': 'Market', 'Evening': 'Home', 'Night': 'Home' },
            'Friday': { 'Morning': 'Home', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Saturday': { 'Morning': 'Temple', 'Afternoon': 'Market', 'Evening': 'Home', 'Night': 'Home' },
            'Sunday': { 'Morning': 'Home', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' }
        },
        
        secrets: [
            'Has been using adult websites late at night',
            'Keeps father\'s photos hidden and talks to them',
            'Has been taking sleeping pills to cope with stress',
            'Sometimes touches herself while thinking of her late husband'
        ],
        
        status: 'Available',
        location: 'Home',
        conquered: false,
        corruption_path: null,
        last_interaction: null
    },
    
    natasha: {
        name: 'Natasha Singh',
        relationship: 'Sister',
        age: 24,
        description: 'Your elder sister. An underpaid HR executive who projects a sanskari image but harbors secret desires for a luxurious lifestyle.',
        
        stats: {
            lust: 15,
            corruption: 5,
            loyalty: 60,
            fear: 0,
            love: 70,
            suspicion: 10,
            resistance: 50
        },
        
        physical: {
            height: '5\'5"',
            build: 'Voluptuous, top-heavy',
            measurements: '36D-28-36',
            hair: 'Shoulder-length black hair',
            eyes: 'Dark brown',
            skin: 'Fair complexion',
            notable: 'Large breasts, curvy figure, always well-dressed despite financial constraints'
        },
        
        personality: {
            traits: ['Ambitious', 'Image-conscious', 'Secretly materialistic', 'Frustrated', 'Validation-seeking'],
            likes: ['Fashion', 'Social media', 'Expensive things', 'Male attention', 'Success stories'],
            dislikes: ['Her low salary', 'Being judged', 'Cheap things', 'Being ignored'],
            fears: ['Social embarrassment', 'Financial failure', 'Being seen as cheap'],
            desires: ['Wealth', 'Status', 'Designer clothes', 'Luxury lifestyle', 'Male validation']
        },
        
        vulnerabilities: [
            'Desperate for financial improvement and status',
            'Craves male validation and attention',
            'Secretly envious of wealthy women',
            'Frustrated with her current life situation',
            'Hidden slutty desires beneath sanskari facade'
        ],
        
        corruption_paths: {
            'sugar_daddy': {
                name: 'The Benefactor',
                description: 'Become her wealthy patron in exchange for favors',
                stages: ['Gift Giving', 'Financial Support', 'Expectation Setting', 'Physical Demands', 'Complete Ownership'],
                difficulty: 'Easy',
                time_required: '1-2 weeks'
            },
            'career_manipulation': {
                name: 'The Mentor',
                description: 'Use connections to advance her career for sexual favors',
                stages: ['Career Advice', 'Job Opportunities', 'Professional Dependency', 'Quid Pro Quo', 'Sexual Servitude'],
                difficulty: 'Medium',
                time_required: '2-3 weeks'
            },
            'social_pressure': {
                name: 'The Influencer',
                description: 'Use social situations and peer pressure',
                stages: ['Social Integration', 'Lifestyle Exposure', 'FOMO Creation', 'Compromise Demands', 'Public Humiliation'],
                difficulty: 'Hard',
                time_required: '3-4 weeks'
            }
        },
        
        schedule_base: {
            'Monday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Tuesday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Wednesday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Thursday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Friday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Mall' },
            'Saturday': { 'Morning': 'Home', 'Afternoon': 'Mall', 'Evening': 'Friends', 'Night': 'Home' },
            'Sunday': { 'Morning': 'Home', 'Afternoon': 'Park', 'Evening': 'Home', 'Night': 'Home' }
        },
        
        secrets: [
            'Has a secret Instagram account with revealing photos',
            'Borrows money from friends for expensive purchases',
            'Has been on dating apps looking for rich men',
            'Fantasizes about being a sugar baby'
        ],
        
        status: 'Available',
        location: 'Home',
        conquered: false,
        corruption_path: null,
        last_interaction: null
    },
    
    tanya: {
        name: 'Tanya Singh',
        relationship: 'Sister',
        age: 22,
        description: 'Your younger sister. A petite firecracker with anger issues and an inferiority complex, working as an underpaid HR assistant.',
        
        stats: {
            lust: 10,
            corruption: 0,
            loyalty: 40,
            fear: 5,
            love: 60,
            suspicion: 20,
            resistance: 70
        },
        
        physical: {
            height: '5\'2"',
            build: 'Petite, athletic',
            measurements: '32B-26-34',
            hair: 'Short black hair',
            eyes: 'Dark brown',
            skin: 'Fair complexion',
            notable: 'Small but perky breasts, tight athletic body, fierce expression'
        },
        
        personality: {
            traits: ['Fiery', 'Aggressive', 'Insecure', 'Competitive', 'Stubborn'],
            likes: ['Fitness', 'Dancing', 'Proving herself', 'Being in control', 'Challenges'],
            dislikes: ['Being underestimated', 'Her small size', 'Being ignored', 'Weakness'],
            fears: ['Being seen as weak', 'Not being taken seriously', 'Failure'],
            desires: ['Respect', 'Power', 'Recognition', 'To be seen as strong', 'Dominance']
        },
        
        vulnerabilities: [
            'Deep inferiority complex about her size and age',
            'Desperate need to prove herself',
            'Responds to displays of dominance',
            'Secretly craves being overpowered',
            'Anger issues mask deep insecurity'
        ],
        
        corruption_paths: {
            'dominance_breaking': {
                name: 'The Alpha',
                description: 'Break her spirit through displays of superior dominance',
                stages: ['Challenge Issued', 'Power Struggle', 'Dominance Established', 'Submission Training', 'Complete Obedience'],
                difficulty: 'Hard',
                time_required: '3-4 weeks'
            },
            'protection_racket': {
                name: 'The Protector',
                description: 'Create dangerous situations then rescue her',
                stages: ['Vulnerability Creation', 'Rescue Scenario', 'Dependency Building', 'Protection Price', 'Sexual Payment'],
                difficulty: 'Medium',
                time_required: '2-3 weeks'
            },
            'competition_corruption': {
                name: 'The Competitor',
                description: 'Use her competitive nature against her',
                stages: ['Challenge Setup', 'Escalating Stakes', 'Impossible Odds', 'Desperate Measures', 'Sexual Forfeits'],
                difficulty: 'Medium',
                time_required: '2-3 weeks'
            }
        },
        
        schedule_base: {
            'Monday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Tuesday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Wednesday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Dance Class', 'Night': 'Home' },
            'Thursday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Friday': { 'Morning': 'Home', 'School-P1': 'Work', 'School-P2': 'Work', 'School-P3': 'Work', 'School-P4': 'Work', 'Lunch': 'Work', 'School-P5': 'Work', 'Afternoon': 'Work', 'Evening': 'Home', 'Night': 'Home' },
            'Saturday': { 'Morning': 'Gym', 'Afternoon': 'Home', 'Evening': 'Club', 'Night': 'Home' },
            'Sunday': { 'Morning': 'Home', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' }
        },
        
        secrets: [
            'Takes steroids to enhance her workout performance',
            'Has been in underground fighting matches',
            'Secretly watches domination porn',
            'Has a crush on her dance instructor'
        ],
        
        status: 'Available',
        location: 'Home',
        conquered: false,
        corruption_path: null,
        last_interaction: null
    },
    
    // School Characters
    kavya: {
        name: 'Kavya Gupta',
        relationship: 'Classmate',
        age: 18,
        description: 'A curvy classmate with weight insecurity and daddy issues. The perfect target for corruption through her vulnerabilities.',
        
        stats: {
            lust: 0,
            corruption: 0,
            loyalty: 0,
            fear: 0,
            love: 0,
            suspicion: 0,
            resistance: 40
        },
        
        physical: {
            height: '5\'4"',
            build: 'Curvy, slightly overweight',
            measurements: '36C-32-38',
            hair: 'Long black hair',
            eyes: 'Dark brown',
            skin: 'Wheatish complexion',
            notable: 'Soft curves, insecure about her weight, beautiful face'
        },
        
        personality: {
            traits: ['Insecure', 'Sweet', 'Naive', 'Studious', 'Emotional'],
            likes: ['Junk food', 'Romantic movies', 'Validation', 'Academic success', 'Father\'s approval'],
            dislikes: ['Being called fat', 'Difficult subjects', 'Criticism', 'Being ignored'],
            fears: ['Rejection', 'Academic failure', 'Disappointing father', 'Being alone'],
            desires: ['Acceptance', 'Love', 'Confidence', 'Academic success', 'Father\'s pride']
        },
        
        vulnerabilities: [
            'Deep insecurity about her weight and appearance',
            'Desperate need for male validation',
            'Daddy issues and need for father figure approval',
            'Academic pressure and fear of failure',
            'Emotional eating and body image problems'
        ],
        
        corruption_paths: {
            'personal_trainer': {
                name: 'The Fitness Guru',
                description: 'Become her personal trainer and corrupt through "exercise"',
                stages: ['Fitness Consultation', 'Private Training', 'Physical Contact', 'Sexual Exercise', 'Fitness Slave'],
                difficulty: 'Easy',
                time_required: '2-3 weeks'
            },
            'academic_savior': {
                name: 'The Tutor',
                description: 'Help with studies in exchange for sexual favors',
                stages: ['Study Help', 'Academic Dependency', 'Payment Demands', 'Sexual Tutoring', 'Academic Slave'],
                difficulty: 'Medium',
                time_required: '2-3 weeks'
            },
            'father_figure': {
                name: 'The Daddy',
                description: 'Exploit her daddy issues for control',
                stages: ['Paternal Care', 'Emotional Dependency', 'Inappropriate Boundaries', 'Sexual Grooming', 'Daddy\'s Girl'],
                difficulty: 'Medium',
                time_required: '3-4 weeks'
            }
        },
        
        schedule_base: {
            'Monday': { 'Morning': 'Home', 'School-P1': 'School', 'School-P2': 'School', 'School-P3': 'School', 'School-P4': 'School', 'Lunch': 'School', 'School-P5': 'School', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Tuesday': { 'Morning': 'Home', 'School-P1': 'School', 'School-P2': 'School', 'School-P3': 'School', 'School-P4': 'School', 'Lunch': 'School', 'School-P5': 'School', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Wednesday': { 'Morning': 'Home', 'School-P1': 'School', 'School-P2': 'School', 'School-P3': 'School', 'School-P4': 'School', 'Lunch': 'School', 'School-P5': 'School', 'Afternoon': 'Tuition', 'Evening': 'Home', 'Night': 'Home' },
            'Thursday': { 'Morning': 'Home', 'School-P1': 'School', 'School-P2': 'School', 'School-P3': 'School', 'School-P4': 'School', 'Lunch': 'School', 'School-P5': 'School', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Friday': { 'Morning': 'Home', 'School-P1': 'School', 'School-P2': 'School', 'School-P3': 'School', 'School-P4': 'School', 'Lunch': 'School', 'School-P5': 'School', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' },
            'Saturday': { 'Morning': 'Home', 'Afternoon': 'Mall', 'Evening': 'Home', 'Night': 'Home' },
            'Sunday': { 'Morning': 'Temple', 'Afternoon': 'Home', 'Evening': 'Home', 'Night': 'Home' }
        },
        
        secrets: [
            'Binges on junk food when stressed',
            'Has romantic fantasies about older men',
            'Cries herself to sleep when feeling fat',
            'Secretly wishes her father paid more attention to her'
        ],
        
        status: 'Unknown',
        location: 'School',
        conquered: false,
        corruption_path: null,
        last_interaction: null
    }
};

/**
 * Character utility functions
 */
export class CharacterManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    
    /**
     * Initialize character data in state manager
     */
    initializeCharacters() {
        Object.keys(characterData).forEach(charId => {
            const charData = characterData[charId];
            const existingChar = this.stateManager.getCharacter(charId);
            
            if (!existingChar) {
                // Create new character with base data
                this.stateManager.state.characters[charId] = {
                    ...charData,
                    // Add runtime data
                    interactions_today: 0,
                    last_interaction: null,
                    relationship_history: [],
                    corruption_progress: {},
                    unlocked_paths: [],
                    current_mood: 'neutral'
                };
            } else {
                // Update existing character with new data while preserving progress
                Object.assign(existingChar, {
                    ...charData,
                    // Preserve existing progress
                    stats: existingChar.stats,
                    conquered: existingChar.conquered,
                    corruption_path: existingChar.corruption_path,
                    last_interaction: existingChar.last_interaction
                });
            }
        });
    }
    
    /**
     * Get character's current mood based on stats and recent interactions
     */
    getCharacterMood(characterId) {
        const char = this.stateManager.getCharacter(characterId);
        if (!char) return 'unknown';
        
        const { lust, corruption, loyalty, fear, suspicion } = char.stats;
        
        if (fear > 60) return 'terrified';
        if (suspicion > 70) return 'suspicious';
        if (corruption > 80) return 'corrupted';
        if (lust > 60) return 'aroused';
        if (loyalty > 80) return 'devoted';
        if (loyalty < 20) return 'rebellious';
        
        return 'neutral';
    }
    
    /**
     * Check if character is available for interaction
     */
    isCharacterAvailable(characterId, location, timeSlot, dayOfWeek) {
        const char = this.stateManager.getCharacter(characterId);
        if (!char) return false;
        
        const schedule = char.schedule_base[dayOfWeek];
        if (!schedule) return false;
        
        return schedule[timeSlot] === location;
    }
    
    /**
     * Get available corruption paths for character
     */
    getAvailableCorruptionPaths(characterId) {
        const charData = characterData[characterId];
        if (!charData || !charData.corruption_paths) return [];
        
        const char = this.stateManager.getCharacter(characterId);
        const playerStats = this.stateManager.getPlayer().stats;
        
        return Object.entries(charData.corruption_paths).filter(([pathId, pathData]) => {
            // Check if player meets requirements for this path
            switch (pathData.difficulty) {
                case 'Easy': return playerStats.cp >= 50;
                case 'Medium': return playerStats.cp >= 100 && playerStats.dxp >= 50;
                case 'Hard': return playerStats.cp >= 200 && playerStats.dxp >= 100;
                default: return true;
            }
        });
    }
}
